<?php
  $misc = new App\Http\Controllers\FrontEnd\MiscellaneousController();

  $language = $misc->getLanguage();
  $pageHeading = $language->pageName()->select('error_page_title')->first();
  $bgImg = $misc->getBreadcrumb();
?>

<?php $__env->startSection('pageHeading'); ?>
  <?php if(!empty($pageHeading)): ?>
    <?php echo e($pageHeading->error_page_title); ?>

  <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
  <?php $pageTitle = !empty($pageHeading) ? $pageHeading->error_page_title : ''; ?>

  <?php if ($__env->exists('frontend.partials.breadcrumb', ['breadcrumb' => $bgImg->breadcrumb, 'title' => $pageTitle])) echo $__env->make('frontend.partials.breadcrumb', ['breadcrumb' => $bgImg->breadcrumb, 'title' => $pageTitle], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <!--====== 404 PART START ======-->
  <section class="error-section">
    <div class="container">
      <div class="row">
        <div class="col-lg-6">
          <img src="<?php echo e(asset('assets/img/404.png')); ?>" alt="404" class="not-found-image">
        </div>

        <div class="col-lg-6">
          <div class="error-txt">
            <div class="oops-img-section">
              <img src="<?php echo e(asset('assets/img/oops.png')); ?>" alt="oops">
            </div>

            <h2><?php echo e(__('You are lost') . '.'); ?></h2>
            <p>
              <?php echo e(__('The page you are looking for') . ' ' . __('might have been moved') . ','); ?><br>
              <?php echo e(__('renamed') . ', ' . __('or might never existed') . '.'); ?>

            </p>

            <a href="<?php echo e(route('index')); ?>" class="main-btn mt-30"><?php echo e(__('Home')); ?></a>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!--====== 404 PART END ======-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/errors/404.blade.php ENDPATH**/ ?>