/*-----------------------------------------------------------
    * Template Name    : Multirent - Multipurpose / Equipment Rental Website
    * Author           : KreativDev
    * File Description : This file contains the javaScript functions for the actual template, this
                        is the file you need to edit to change the functionality of the template.
    *------------------------------------------------------------
*/

!(function($) {
    "use strict";

    /*============================================
        Mobile menu
    ============================================*/
    var mobileMenu = function() {
        // Variables
        var body = $("body"),
            mainNavbar = $(".main-navbar"),
            mobileNavbar = $(".mobile-menu"),
            cloneInto = $(".mobile-menu-wrapper"),
            cloneItem = $(".mobile-item"),
            menuToggler = $(".menu-toggler"),
            offCanvasMenu = $("#offcanvasMenu"),
            backdrop,
            _initializeBackDrop = function() {
                backdrop = document.createElement('div');
                backdrop.className = 'menu-backdrop';
                backdrop.onclick = function hideOffCanvas() {
                    menuToggler.removeClass("active"),
                        body.removeClass("mobile-menu-active"),
                        backdrop.remove();
                };
                document.body.appendChild(backdrop);
            };

        menuToggler.on("click", function() {
            $(this).toggleClass("active");
            body.toggleClass("mobile-menu-active");
            _initializeBackDrop();
            if (!body.hasClass("mobile-menu-active")) {
                $('.menu-backdrop').remove();
            }
        })

        mainNavbar.find(cloneItem).clone(!0).appendTo(cloneInto);

        if (offCanvasMenu) {
            body.find(offCanvasMenu).clone(!0).appendTo(cloneInto);
        }

        mobileNavbar.find("li").each(function(index) {
            var toggleBtn = $(this).children(".toggle")
            toggleBtn.on("click", function(e) {
                $(this)
                    .parent("li")
                    .children("ul")
                    .stop(true, true)
                    .slideToggle(350);
                $(this).parent("li").toggleClass("show");
            })
        })

        // check browser width in real-time
        var checkBreakpoint = function() {
            var winWidth = window.innerWidth;
            if (winWidth <= 1199) {
                mainNavbar.hide();
                mobileNavbar.show()
            } else {
                mainNavbar.show();
                mobileNavbar.hide();
                $('.menu-backdrop').remove();
            }
        }
        checkBreakpoint();

        $(window).on('resize', function() {
            checkBreakpoint();
        });
    }
    mobileMenu();

    var getHeaderHeight = function() {
        var headerNext = $(".header-next");
        var header = headerNext.prev(".header-area");
        var headerHeight = header.height();

        headerNext.css({
            "margin-top": headerHeight
        })
    }
    getHeaderHeight();

    $(window).on('resize', function() {
        getHeaderHeight();
    });


    /*============================================
        Navlink active class
    ============================================*/
    // var a = $("#mainMenu .nav-link"),
    //     c = window.location;
    // for (var i = 0; i < a.length; i++) {
    //     const el = a[i];

    //     if (el.href == c) {
    //         el.classList.add("active");
    //     }
    // }


    /*============================================
        Sticky header
    ============================================*/
    $(window).on("scroll", function() {
        var header = $(".header-area");
        // If window scroll down .is-sticky class will added to header
        if ($(window).scrollTop() >= 100) {
            header.addClass("is-sticky");
        } else {
            header.removeClass("is-sticky");
        }
    });


    /*============================================
        Image to background image
    ============================================*/
    var bgImage = $(".bg-img")
    bgImage.each(function() {
        var el = $(this),
            src = el.attr("data-bg-img");

        el.css({
            "background-image": "url(" + src + ")",
            "background-repeat": "no-repeat"
        });
    });


    /*============================================
          Youtube popup
      ============================================*/
    $(".youtube-popup").magnificPopup({
        disableOn: 300,
        type: "iframe",
        mainClass: "mfp-fade",
        removalDelay: 160,
        preloader: false,
        fixedContentPos: false
    })


    /*============================================
        Go to top
    ============================================*/
    $(window).on("scroll", function() {
        // If window scroll down .active class will added to go-top
        var goTop = $(".go-top");

        if ($(window).scrollTop() >= 200) {
            goTop.addClass("active");
        } else {
            goTop.removeClass("active")
        }
    })
    $(".go-top").on("click", function(e) {
        $("html, body").animate({
            scrollTop: 0,
        }, 0);
    });


    /*============================================
        Lazyload image
    ============================================*/
    var lazyLoad = function() {
        window.lazySizesConfig = window.lazySizesConfig || {};
        window.lazySizesConfig.loadMode = 2;
        lazySizesConfig.preloadAfterLoad = true;
    }


    /*============================================
        Nice select
    ============================================*/
    $(".niceselect").niceSelect();

    var selectList = $(".nice-select .list")
    $(".nice-select .list").each(function() {
        var list = $(this).children();
        if (list.length > 5) {
            $(this).css({
                "height": "160px",
                "overflow-y": "scroll"
            })
        }
    })


    /*============================================
        Footer date
    ============================================*/
    var date = new Date().getFullYear();
    $("#footerDate").text(date);


    /*============================================
        Date-range Picker
    ============================================*/
    // $('input[name="date"]').daterangepicker({
    //     opens: 'left',
    //     "singleDatePicker": true,
    //     autoUpdateInput: false,
    //     locale: {
    //         format: 'YYYY-MM-DD'
    //     }
    // })
    $('input[name="date"]').daterangepicker({
        "singleDatePicker": true,
        autoUpdateInput: false,
        locale: {
            format: 'YYYY-MM-DD'
        }
    });
    $('input[name="date"]').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD'));
    });
    $('input[name="date"]').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('');
    });


    /*============================================
        Toggle List
    ============================================*/
    $("[data-toggle-list]").each(function(i) {
        var list = $(this).children();
        var listShow = $(this).data("toggle-show");
        var listShowBtn = $(this).next("[data-toggle-btn]");

        if (list.length > listShow) {
            listShowBtn.show()
            list.slice(listShow).toggle(300);

            listShowBtn.on("click", function() {
                list.slice(listShow).slideToggle(300);
                $(this).text($(this).text() === "Show Less -" ? "Show More +" : "Show Less -")
            })
        } else {
            listShowBtn.hide()
        }
    })


    /*============================================
        Tabs mouse hover animation
    ============================================*/
    $("[data-hover='fancyHover']").mouseHover();


    /*============================================
        Sliders
    ============================================*/
    // Home Slider 1
    var homeSlider1 = new Swiper("#home-slider-1", {
        loop: true,
        speed: 2000,
        parallax: true,
        slidesPerView: 1,
        autoplay: true,
        effect: 'fade',

        pagination: {
            el: '#home-slider-1-pagination',
            clickable: true
        },

        on: {
            slideChange: function() {
                var doAnimations = function(elements) {
                    var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
                    elements.each(function() {
                        var animationDelay = $(this).data('delay');
                        var animationType = 'animate__animated ' + $(this).data('animation');
                        $(this).css({
                            'animation-delay': animationDelay,
                            '-webkit-animation-delay': animationDelay
                        });
                        $(this).addClass(animationType).one(animationEndEvents, function() {
                            $(this).removeClass(animationType);
                        });
                    });
                }
                var firstAnimatingElements = $('.swiper-slide').find('[data-animation]');
                doAnimations(firstAnimatingElements);
            },
        },
    });
    var homeImageSlider1 = new Swiper("#home-img-slider-1", {
        loop: true,
        speed: 1500,
        effect: 'fade',
        slidesPerView: 1
    });
    // Sync both slider
    homeImageSlider1.controller.control = homeSlider1;
    homeSlider1.controller.control = homeImageSlider1;

    // Home Slider 2
    var homeSlider2 = new Swiper("#home-slider-2", {
        loop: true,
        speed: 1000,
        slidesPerView: 1,
        effect: "fade",
        fadeEffect: {
            crossFade: true
        },
        allowTouchMove: false
    });
    var homeImageSlider2 = new Swiper("#home-img-slider-2", {
        loop: true,
        speed: 1000,
        slidesPerView: 1,
        autoplay: true,
        pagination: {
            el: "#home-img-slider-2-pagination",
            clickable: true,
        },
    });
    // Sync both slider
    homeImageSlider2.controller.control = homeSlider2;

    // Home Slider 3
    var homeSlider3 = new Swiper("#home-slider-3", {
        loop: true,
        speed: 2000,
        parallax: true,
        slidesPerView: 1,
        autoplay: true,
        effect: 'fade',

        pagination: {
            el: '#home-slider-3-pagination',
            clickable: true
        },

        on: {
            slideChange: function() {
                var doAnimations = function(elements) {
                    var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
                    elements.each(function() {
                        var animationDelay = $(this).data('delay');
                        var animationType = 'animate__animated ' + $(this).data('animation');
                        $(this).css({
                            'animation-delay': animationDelay,
                            '-webkit-animation-delay': animationDelay
                        });
                        $(this).addClass(animationType).one(animationEndEvents, function() {
                            $(this).removeClass(animationType);
                        });
                    });
                }
                var firstAnimatingElements = $('.swiper-slide').find('[data-animation]');
                doAnimations(firstAnimatingElements);
            },
        },
    });
    var homeImageSlider3 = new Swiper("#home-img-slider-3", {
        loop: true,
        speed: 1500,
        effect: 'fade',
        slidesPerView: 1
    });
    // Sync both slider
    homeImageSlider3.controller.control = homeSlider3;
    homeSlider3.controller.control = homeImageSlider3;

    // Home Slider 4
    var homeSlider4 = new Swiper("#home-slider-4", {
        loop: true,
        speed: 1000,
        slidesPerView: 1,
        effect: "fade",
        fadeEffect: {
            crossFade: true
        },
        allowTouchMove: false,

        pagination: {
            el: "#home-slider-4-pagination",
            clickable: true,
            renderBullet: function(index, className) {
                return '<span class="' + className + '">' + "0" + (index + 1) + "</span>";
            },
        },
    });
    var homeImageSlider2 = new Swiper("#home-img-slider-4", {
        loop: true,
        speed: 1000,
        slidesPerView: 1,
        autoplay: true,
        pagination: {
            el: "#home-img-slider-4-pagination",
            clickable: true,
        },
    });
    // Sync both slider
    homeImageSlider2.controller.control = homeSlider4;

    // Testimonial Slider
    var testimonialSlider1 = new Swiper("#testimonial-slider-1", {
        speed: 800,
        spaceBetween: 25,
        loop: true,
        slidesPerView: 2,

        // Pagination
        pagination: {
            el: '#testimonial-slider-1-pagination',
            clickable: true
        },

        breakpoints: {
            // when window width is >= 320px
            320: {
                slidesPerView: 1
            },
            // when window width is >= 400px
            992: {
                slidesPerView: 2
            }
        }
    });
    var testimonialSlider2 = new Swiper("#testimonial-slider-2", {
        slidesPerView: 2,
        grid: {
            rows: 2,
        },

        // Navigation arrows
        navigation: {
            nextEl: "#testimonial-slider-2-next",
            prevEl: "#testimonial-slider-2-prev",
        },

        breakpoints: {
            320: {
                slidesPerView: 1,
                grid: {
                    rows: 0,
                },
            },
            992: {
                slidesPerView: 2,
                grid: {
                    rows: 2,
                },
            },
        }
    });
    var testimonialSlider3 = new Swiper("#testimonial-slider-3", {
        speed: 800,
        spaceBetween: 25,
        loop: true,
        slidesPerView: 3,

        // Pagination
        pagination: {
            el: '#testimonial-slider-3-pagination',
            clickable: true
        },

        breakpoints: {
            320: {
                slidesPerView: 1
            },
            992: {
                slidesPerView: 2
            },
            1400: {
                slidesPerView: 3
            },
        }
    });

    // Product Slider
    $(".product-slider").each(function() {
        var id = $(this).attr("id");
        var sliderId = "#" + id;

        var swiper = new Swiper(sliderId, {
            speed: 800,
            spaceBetween: 25,
            loop: false,
            slidesPerView: 3,

            // Pagination
            pagination: {
                el: sliderId + '-pagination',
                clickable: true
            },

            breakpoints: {
                320: {
                    slidesPerView: 1
                },
                768: {
                    slidesPerView: 2
                },
                1200: {
                    slidesPerView: 3
                },
            }
        })
    })
    $(".product-inline-slider").each(function() {
        var id = $(this).attr("id");
        var sliderId = "#" + id;

        var swiper = new Swiper(sliderId, {
            speed: 800,
            spaceBetween: 25,
            loop: false,
            slidesPerView: 2,

            // Pagination
            pagination: {
                el: sliderId + '-pagination',
                clickable: true
            },

            breakpoints: {
                320: {
                    slidesPerView: 1
                },
                768: {
                    slidesPerView: 2
                }
            }
        })
    })


    // add user email for subscription
    $('.subscription-form').on('submit', function(event) {
        event.preventDefault();

        let formURL = $(this).attr('action');
        let formMethod = $(this).attr('method');

        let formData = new FormData($(this)[0]);

        $.ajax({
            url: formURL,
            method: formMethod,
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                $('input[name="email_id"]').val('');

                toastr['success'](response.success);
            },
            error: function(errorData) {
                toastr['error'](errorData.responseJSON.error.email_id[0]);
            }
        });
    });

    // Category Slider
    var categorySlider1 = new Swiper("#category-slider-1", {
        speed: 800,
        spaceBetween: 0,
        loop: false,
        slidesPerView: 6,

        // Pagination
        pagination: {
            el: '#category-slider-1-pagination',
            clickable: true
        },

        breakpoints: {
            320: {
                slidesPerView: 1
            },
            768: {
                slidesPerView: 2
            },
            992: {
                slidesPerView: 3
            },
            1200: {
                slidesPerView: 5
            },
            1440: {
                slidesPerView: 6
            },
        }
    })
    var categorySlider2 = new Swiper("#category-slider-2", {
        speed: 800,
        spaceBetween: 0,
        loop: false,
        slidesPerView: 6,
        spaceBetween: 24,

        // Pagination
        pagination: {
            el: '#category-slider-2-pagination',
            clickable: true
        },

        breakpoints: {
            320: {
                slidesPerView: 1
            },
            768: {
                slidesPerView: 2
            },
            992: {
                slidesPerView: 3
            },
            1200: {
                slidesPerView: 5
            },
            1440: {
                slidesPerView: 6
            },
        }
    })



    // floating whatsapp
    if (whatsappStatus == 1) {
        $('.whatsapp-btn').floatingWhatsApp({
            phone: whatsappNumber,
            popupMessage: whatsappPopupMessage,
            showPopup: whatsappPopupStatus == 1 ? true : false,
            headerTitle: whatsappHeaderTitle,
            position: 'right'
        });
    }
})(jQuery);

$(window).on("load", function() {
    const delay = 350;

    /*============================================
    Preloader
    ============================================*/
    $("#preLoader").delay(delay).fadeOut('slow');

    /*============================================
        Aos animation
    ============================================*/
    var aosAnimation = function() {
        AOS.init({
            easing: "ease",
            duration: 1500,
            once: true,
            offset: 60,
            disable: 'mobile'
        });
    }
    if ($("#preLoader")) {
        setTimeout(() => {
            aosAnimation()
        }, delay);
    } else {
        aosAnimation();
    }
})
