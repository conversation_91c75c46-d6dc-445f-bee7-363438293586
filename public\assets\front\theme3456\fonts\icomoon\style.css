@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?2fejm9');
  src:  url('fonts/icomoon.eot?2fejm9#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?2fejm9') format('truetype'),
    url('fonts/icomoon.woff?2fejm9') format('woff'),
    url('fonts/icomoon.svg?2fejm9#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="ico-"], [class*=" ico-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ico-dumbbell:before {
  content: "\e90c";
}
.ico-package:before {
  content: "\e90e";
}
.ico-free-delivery:before {
  content: "\e90f";
}
.ico-search2:before {
  content: "\e910";
}
.ico-order:before {
  content: "\e911";
}
.ico-money:before {
  content: "\e912";
}
.ico-support:before {
  content: "\e913";
}
.ico-by-cycle:before {
  content: "\e914";
}
.ico-truck:before {
  content: "\e915";
}
.ico-peoples:before {
  content: "\e916";
}
.ico-camera:before {
  content: "\e90d";
}
.ico-clean-car:before {
  content: "\e905";
}
.ico-search:before {
  content: "\e906";
}
.ico-parcel:before {
  content: "\e908";
}
.ico-gift:before {
  content: "\e909";
}
.ico-checkup:before {
  content: "\e90a";
}
.ico-drone:before {
  content: "\e90b";
}
.ico-receive:before {
  content: "\e900";
}
.ico-select:before {
  content: "\e901";
}
.ico-find-car:before {
  content: "\e902";
}
.ico-car-security:before {
  content: "\e903";
}
.ico-quality:before {
  content: "\e904";
}
.ico-rating:before {
  content: "\e907";
}
