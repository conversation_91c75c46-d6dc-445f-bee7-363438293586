<script>
  'use strict';
  const baseURL = "<?php echo e(url('/')); ?>";
  const vapid_public_key = "<?php echo e(env('VAPID_PUBLIC_KEY')); ?>";
  const langDir = <?php echo e($currentLanguageInfo->direction); ?>;
  const whatsappStatus = <?php echo e($basicInfo->whatsapp_status); ?>;
  const whatsappNumber = '<?php echo e($basicInfo->whatsapp_number); ?>';
  const whatsappPopupMessage = `<?php echo $basicInfo->whatsapp_popup_message; ?>`;
  const whatsappPopupStatus = <?php echo e($basicInfo->whatsapp_popup_status); ?>;
  const whatsappHeaderTitle = '<?php echo e($basicInfo->whatsapp_header_title); ?>';
</script>


<script type="text/javascript" src="<?php echo e(asset('assets/js/jquery-3.6.0.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/modernizr-3.6.0.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/popper.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/bootstrap.min.js')); ?>"></script>


<script type="text/javascript" src="<?php echo e(asset('assets/js/jquery.magnific-popup.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/slick.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/toastr.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/datatables-1.10.23.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/datatables.bootstrap4.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/jquery-ui.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/jquery-syotimer.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/daterangepicker.min.js')); ?>"></script>

<?php if(session()->has('success')): ?>
  <script>
    'use strict';
    toastr['success']("<?php echo e(__(session('success'))); ?>");
  </script>
<?php endif; ?>

<?php if(session()->has('error')): ?>
  <script>
    'use strict';
    toastr['error']("<?php echo e(__(session('error'))); ?>");
  </script>
<?php endif; ?>

<?php if(session()->has('warning')): ?>
  <script>
    'use strict';
    toastr['warning']("<?php echo e(__(session('warning'))); ?>");
  </script>
<?php endif; ?>


<script type="text/javascript" src="<?php echo e(asset('assets/js/vanilla-lazyload.min.js')); ?>"></script>


<script type="text/javascript" src="<?php echo e(asset('assets/js/push-notification.js')); ?>"></script>


<script type="text/javascript" src="<?php echo e(asset('assets/js/floating-whatsapp.js')); ?>"></script>
<script type="text/javascript" src="<?php echo e(asset('assets/js/jquery.nice-select.min.js')); ?>"></script>

<?php if(!request()->routeIs('index') && $basicInfo->theme_version == 3 || $basicInfo->theme_version == 4 || $basicInfo->theme_version == 5 || $basicInfo->theme_version == 6): ?>
  <script src="<?php echo e(asset('assets/front/theme3456/js/vendors/bootstrap.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/front/theme3456/js/vendors/lazysizes.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/front/theme3456/js/header-footer.js')); ?>"></script>
<?php endif; ?>

<script type="text/javascript" src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

<script>
  let ReadMore = "<?php echo e(__('Read More')); ?>";
  let ReadLess = "<?php echo e(__('Read Less')); ?>";
  let demo_mode = "<?php echo e(env('DEMO_MODE')); ?>";
</script>

<script type="text/javascript" src="<?php echo e(asset('assets/js/update.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/pwa.js')); ?>" defer></script>


<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/partials/scripts.blade.php ENDPATH**/ ?>