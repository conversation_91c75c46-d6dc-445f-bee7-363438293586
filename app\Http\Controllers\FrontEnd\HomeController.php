<?php

namespace App\Http\Controllers\FrontEnd;

use App\Models\Journal\Blog;
use App\Models\Shop\Product;
use Illuminate\Http\Request;
use App\Models\HomePage\Partner;
use App\Models\HomePage\Section;
use App\Models\BasicSettings\Basic;
use App\Http\Controllers\Controller;
use App\Models\Instrument\Equipment;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\FrontEnd\MiscellaneousController;
use App\Http\Controllers\FrontEnd\PaymentGateway\MyfatoorahController;

class HomeController extends Controller
{
    public function invoice()
    {
        return view('frontend.equipment.invoice');
    }
    public function index()
    {
        $themeVersion = Basic::query()->pluck('theme_version')->first();

        $secInfo = Section::query()->first();

        $misc = new MiscellaneousController();

        $language = $misc->getLanguage();

        $queryResult['seoInfo'] = $language->seoInfo()->select('meta_keyword_home', 'meta_description_home')->first();

        if ($themeVersion == 1 || $themeVersion == 3 || $themeVersion == 4 || $themeVersion == 5) {
            $queryResult['sliderInfos'] = $language->sliderInfo()->orderByDesc('id')->get();
        } elseif ($themeVersion == 2 || $themeVersion == 6) {
            $queryResult['staticInfo'] = $language->staticSecInfo()->first();
        }

        $queryResult['heroSectionImage'] = Basic::query()->pluck('hero_section_image')->first();
        $queryResult['equipmentCategorySection'] = $language->equipmentCategorySection()->first();
        $queryResult['equipCategories'] = $language->equipmentCategory()->where('status', 1)->orderBy('serial_number', 'asc')->get();
        $queryResult['equipment_featured_Categories'] = $language->equipmentCategory()->where('status', 1)->where('is_featured', 'yes')->orderBy('serial_number', 'asc')->get();

        if ($secInfo->about_section_status == 1) {
            $queryResult['aboutSectionImage'] = Basic::query()->pluck('about_section_image')->first();
            $queryResult['aboutSecInfo'] = $language->aboutSection()->first();
        }

        if ($secInfo->work_process_section_status == 1) {
            $queryResult['workProcessSecInfo'] = $language->workProcessSection()->first();
            $queryResult['processes'] = $language->workProcess()->orderBy('serial_number', 'asc')->get();
        }

        if ($secInfo->feature_section_status == 1) {
            $queryResult['featureSecInfo'] = $language->featureSection()->first();
            $queryResult['features'] = $language->feature()->orderByDesc('id')->get();
        }

        if ($secInfo->counter_section_status == 1) {
            $queryResult['counterSectionInfo'] = Basic::query()->select('counter_section_image', 'counter_section_title')->first();
            $queryResult['counters'] = $language->counterInfo()->orderByDesc('id')->get();
        }
        if (($themeVersion == 3 || $themeVersion == 4 || $themeVersion == 5) && $secInfo->video_section_status == 1) {
            $queryResult['videoSection'] = $language->videoSec()->first();
        }

        if ($secInfo->equipment_section_status == 1) {
            $queryResult['equipmentSecInfo'] = $language->equipmentSection()->first();

            $allEquipment = Equipment::query()
                ->join('equipment_contents', 'equipments.id', '=', 'equipment_contents.equipment_id')
                ->join('equipment_categories', 'equipment_categories.id', '=', 'equipment_contents.equipment_category_id')
                ->where('equipments.is_featured', '=', 'yes')
                ->where('equipment_contents.language_id', '=', $language->id)
                ->select('equipments.id', 'equipments.thumbnail_image', 'equipments.vendor_id', 'equipments.lowest_price', 'equipment_contents.title', 'equipment_contents.slug', 'equipments.per_day_price', 'equipments.per_week_price', 'equipments.per_month_price', 'equipment_categories.name as categoryName', 'equipment_categories.slug as categorySlug', 'equipment_contents.features')
                ->orderByDesc('equipments.id')
                ->get();

            $allEquipment->map(function ($equipment) {
                $avgRating = $equipment->review()->avg('rating');
                $ratingCount = $equipment->review()->count();

                $equipment['avgRating'] = floatval($avgRating);
                $equipment['ratingCount'] = $ratingCount;
            });

            $queryResult['allEquipment'] = $allEquipment;
        }

        $cat_equipments = [];
        if (($themeVersion == 6 || $themeVersion == 3 || $themeVersion == 5 || $themeVersion == 4) && $secInfo->product_section_status == 1) {
            foreach ($queryResult['equipment_featured_Categories'] as $k => $vp_v) {
                foreach ($vp_v->equipmentContent()->orderBy('equipment_id', 'desc')->get() as $key => $v) {
                    $cat_equipments[] = $v;
                }
            }
            $queryResult['cat_equipments'] = $cat_equipments;
        }      

        $queryResult['currencyInfo'] = $this->getCurrencyInfo();

        if ($secInfo->testimonial_section_status == 1) {
            $queryResult['testimonialSecInfo'] = $language->testimonialSection()->first();
            $queryResult['testimonials'] = $language->testimonial()->orderByDesc('id')->get();
        }

        if ($themeVersion == 1 && $secInfo->call_to_action_section_status == 1) {
            $queryResult['callToActionSectionImage'] = Basic::query()->pluck('call_to_action_section_image')->first();
            $queryResult['callToActionSecInfo'] = $language->callToActionSection()->first();
        }

        if ($themeVersion == 2 && $secInfo->product_section_status == 1) {
            $queryResult['productSecInfo'] = $language->productSection()->first();

            $queryResult['products'] = Product::query()
                ->join('product_contents', 'products.id', '=', 'product_contents.product_id')
                ->where('products.is_featured', '=', 'yes')
                ->where('product_contents.language_id', '=', $language->id)
                ->select('products.id', 'products.featured_image', 'product_contents.title', 'product_contents.slug', 'products.current_price', 'products.previous_price')
                ->orderByDesc('products.id')
                ->get();
        }

        $cat_products = [];
        if (($themeVersion == 6 || $themeVersion == 3 || $themeVersion == 5 || $themeVersion == 4) && $secInfo->product_section_status == 1) {
            $queryResult['productSecInfo'] = $language->productSection()->first();
            $queryResult['product_categories'] = $language->productCategory;
            foreach ($queryResult['product_categories'] as $k => $vp_v) {
                foreach ($vp_v->productContent as $key => $v) {
                    $cat_products[] = $v;
                }
            }
            $queryResult['cat_products'] = $cat_products;
        }

        // dd($queryResult['cat_products'][0]->category);

        if ($secInfo->blog_section_status == 1) {
            $queryResult['blogSecInfo'] = $language->blogSection()->first();

            $queryResult['blogs'] = Blog::query()
                ->join('blog_informations', 'blogs.id', '=', 'blog_informations.blog_id')
                ->join('blog_categories', 'blog_categories.id', '=', 'blog_informations.blog_category_id')
                ->where('blog_informations.language_id', '=', $language->id)
                ->select('blogs.image', 'blog_categories.name AS categoryName', 'blog_categories.slug AS categorySlug', 'blog_informations.title', 'blog_informations.slug', 'blog_informations.author', 'blogs.created_at', 'blog_informations.content')
                ->limit(3)
                ->orderBy('blogs.serial_number', 'asc')
                ->get();
        }
        if ($secInfo->partner_section_status == 1) {
            $queryResult['partners'] = Partner::query()->orderBy('serial_number', 'asc')->get();
        }

        $queryResult['secInfo'] = $secInfo;
        // dd($queryResult);
        // dd($queryResult['staticInfo']);
        return view('frontend.home.index-v' . $themeVersion, $queryResult);
    }

    public function myfatoorah_callback(Request $request)
    {
        $type = Session::get('myfatoorah_payment_type');

        if ($type == 'product purchase') {
            $data = new MyfatoorahController();
            $data = $data->successCallback($request);
            // return redirect($data);
            Session::forget('myfatoorah_payment_type');
            if ($data['status'] == 'success') {
                return redirect()->route('shop.purchase_product.complete');
            } else {
                return redirect()->route('shop.purchase_product.cancel');
            }
        } elseif ($type == 'equipment booking') {
            $data = new MyfatoorahController();
            $data = $data->successCallback($request);
            Session::forget('myfatoorah_payment_type');
            if ($data['status'] == 'success') {
                return redirect()->route('equipment.make_booking.complete');
            } else {
                return redirect()->route('equipment.make_booking.cancel');
            }
        }
    }

    public function myfatoorah_cancel(Request $request)
    {
        return redirect()
            ->route('index')
            ->with(['alert-type' => 'error', 'message' => 'Payment failed']);
    }

    //offline
    public function offline()
    {
        return view('frontend.offline');
    }
}
