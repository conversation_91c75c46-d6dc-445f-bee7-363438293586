/* -----------------------------------------
	Header CSS
----------------------------------------- */
.header-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

.header-area.header-static {
    position: relative;
}

.header-area .cart-btn .btn-icon{
  position: relative;
  padding-inline-end: 12px;
  font-size: 14px;
}
.header-area .cart-btn .btn-icon span{
  position: absolute;
  top: -12px;
  right: 0;
  left: 0;
  width: fit-content;
  margin-inline-start: auto;
  margin-inline-end: 0;
  padding: 1px 5px;
  color: var(--text-white);
  font-size: var(--font-xsm);
  border-radius: 50%;
  line-height: 1.4;
  background-color: var(--color-primary);
}

.header-area.is-sticky {
    position: fixed;
    background: rgba(var(--color-white-rgb), 0.9);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px);
    box-shadow: 0px 8px 60px rgba(24, 24, 24, 0.1);
    animation: slideDown 0.5s;
}

.header-area.is-sticky .nav-link {
    color: var(--text-dark);
}

.header-area.is-sticky .nav-link.active {
    color: var(--color-primary);
}

.header-area .more-option {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.header-area .more-option .more-option-toggle {
    z-index: 11;
    font-size: 25px;
    padding: 10px;
}

.header-area .more-option .item {
    position: relative;
}

.header-area .more-option .item:not(:last-child) {
    margin-inline-end: 16px;
}

.header-area .navbar-nav {
    margin-inline-start: auto;
    margin-inline-end: 60px;
}

.header-area .nav-link {
    position: relative;
    padding-top: 30px;
    padding-bottom: 30px;
    color: var(--text-dark);
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
}

.header-area .nav-link.toggle i {
    font-size: 12px;
    margin-inline-start: 2px;
    transition: transform 0.4s ease-out;
}

.header-area .nav-link.toggle:hover i {
    transform: rotate(-90deg);
}

.header-area .nav-link:hover .menu-dropdown {
    transform: none;
}

.header-area .nav-link:hover,
.header-area .nav-link.active,
.header-area .nav-link:focus {
    color: var(--color-primary) !important;
}

.header-area .main-navbar .navbar {
    position: inherit;
    padding: 0;
}

.header-area .main-navbar .navbar .navbar-brand {
    padding: 0;
    margin: 0;
}

.header-area .main-navbar .nav-item {
    position: relative;
    margin-left: 10px;
    margin-right: 10px;
}

.header-area .main-navbar .menu-dropdown {
    position: absolute;
    top: auto;
    left: 0;
    z-index: 99;
    opacity: 0;
    width: 200px;
    display: block;
    visibility: hidden;
    list-style: none;
    margin: 0;
    padding: 0;
    padding: 10px 0px 10px;
    background-color: var(--bg-white);
    box-shadow: 0px 0px 30px 0px rgba(var(--color-dark-rgb), 0.15);
    transform: translateY(10%);
    transition: transform 0.3s ease-out;
}

.header-area .main-navbar .menu-dropdown .nav-item {
    margin: 0;
}

.header-area .main-navbar .menu-dropdown .nav-link {
    display: block;
    padding: 5px 30px;
    font-size: var(--font-sm);
    border-inline-start: 2px solid transparent;
}

.header-area .main-navbar .menu-dropdown .nav-link:hover {
    color: var(--color-primary);
    background-color: var(--bg-1);
}

.header-area .main-navbar .menu-dropdown .nav-link:hover.toggle i {
    transform: translateY(-50%) rotate(-90deg);
}

.header-area .main-navbar .menu-dropdown .nav-link.toggle i {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

.header-area .main-navbar .menu-dropdown .nav-link.active {
    border-inline-start-color: var(--color-primary);
}

.header-area .main-navbar .menu-dropdown .menu-dropdown .nav-item {
    margin: 0;
}

.header-area .main-navbar .menu-dropdown .menu-dropdown .nav-item a:hover::before {
    transform: scaleY(0);
}

.header-area .main-navbar .menu-dropdown .menu-dropdown .nav-item a.active::before {
    transform: scaleY(0);
}

.header-area .menu-toggler {
    width: 34px;
    height: 34px;
    padding: 0;
    border: 0;
    background: var(--color-primary);
    z-index: 1000;
}

.header-area .menu-toggler:focus {
    box-shadow: none !important;
}

.header-area .menu-toggler span {
    position: relative;
    display: block;
    width: 22px;
    margin-inline: auto;
    height: 2px;
    background: var(--color-white);
    border-radius: 10px;
    transition: transform 200ms 0ms, top 200ms 160ms;
}

.header-area .menu-toggler span:nth-child(1) {
    top: -4px;
}

.header-area .menu-toggler span:nth-child(3) {
    top: 4px;
}

.header-area .menu-toggler.active span:nth-child(1) {
    top: 0;
    transform: rotateZ(45deg);
    transition: top 200ms 0ms, transform 200ms 160ms;
}

.header-area .menu-toggler.active span:nth-child(2) {
    transform: scale(0);
}

.header-area .menu-toggler.active span:nth-child(3) {
    top: -4px;
    transform: rotateZ(-45deg);
    transition: top 200ms 0ms, transform 200ms 160ms;
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown {
    top: 0;
    opacity: 0;
    left: 200px;
    visibility: hidden;
    transform: translateY(5px);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li.active a {
    color: var(--color-primary);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item .menu-dropdown li:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .main-navbar .navbar-nav .nav-item:hover .menu-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.header-area .nice-select {
    border: none;
    padding-inline-end: 0;
    padding-inline-start: 22px;
    background: transparent;
    color: var(--text-dark);
    font-size: var(--font-sm);
}

.header-area .nice-select::after {
    width: auto;
    height: auto;
    border-radius: 0;
    border: none;
    content: "\f0ac";
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    top: 50%;
    left: 0;
    font-weight: 300;
    margin: 0;
    transform: translateY(-50%);
}

.header-area .nice-select .list {
    min-width: 100px;
    height: auto;
    background-color: var(--text-white);
    box-shadow: 0px 0px 30px 0px rgba(var(--color-medium-rgb), 0.15);
}

.header-area .nice-select .list li {
    min-height: 30px;
    line-height: 30px;
}

.header-area .btn-icon {
    font-size: 20px;
    font-weight: 300;
    color: var(--text-dark);
    width: auto;
    height: auto;
    background-color: transparent;
}

.header-area .btn-icon:hover {
    color: var(--color-primary);
    background: transparent;
}

.header-area .main-responsive-nav {
    display: none;
}

.header-area .mobile-menu {
    visibility: hidden;
}

/* -----------------------------------------
	Header 1 CSS
----------------------------------------- */
.header-area.header_v1 {
    box-shadow: none;
}

.header-area.header_v1.is-sticky:is(.nav-link:not(:is(.active, .menu-dropdown .nav-link)), .btn-icon, .nice-select, .nice-select::after),
.header-area.header_v1 :is(.nav-link:not(:is(.active, .menu-dropdown .nav-link)), .btn-icon, .nice-select, .nice-select::after) {
    color: var(--text-white);
}

.header-area.header_v1 .nav-link.active {
    color: var(--color-primary);
}

.header-area.header_v1 .nice-select::after {
    color: var(--text-white);
}

.header-area.header_v1 .nice-select li {
    color: var(--text-dark);
}

.header-area.header_v1.is-sticky {
    background: rgba(var(--color-dark-rgb), 0.9);
}

@media (max-width: 1199px) {
    .header-area.header_v1 {
        background: rgba(var(--color-dark-rgb), 0.9);
    }

    .header-area.header_v1:not(.is-sticky) {
        background: transparent;
        box-shadow: unset;
        -webkit-backdrop-filter: unset;
        backdrop-filter: unset;
    }

    .header-area.header_v1 .mobile-menu {
        background: var(--color-dark);
    }

    .header-area.header_v1 :is(.nav-link, .btn-icon, .nice-select, .nice-select::after) {
        color: var(--text-white);
    }

    .header-area.header_v1.is-sticky .nav-link {
        color: var(--text-white) !important;
    }

    .header-area.header_v1.is-sticky .nav-link.active {
        color: var(--color-primary);
    }
}

/* -----------------------------------------
	Header 2 CSS
----------------------------------------- */
@media (min-width: 992px) {
    .custom-container {
        padding: 0 15px;
        box-shadow: var(--shadow-md);
        background-color: var(--bg-white);
        transition: all 0.2s;
    }
}
@media (min-width: 1200px) {
    .header-area.header_v2:not(.is-sticky) .nice-select li {
        color: var(--text-dark);
    }

    .header-area.header_v2 .btn-outline {
        border-color: var(--color-primary);
        color: var(--color-primary);
    }

    .header-area.header_v2 .custom-container {
        margin-top: 20px;
    }

    .header-area.header_v2.is-sticky .custom-container {
        background-color: transparent !important;
        margin-top: 0;
        padding-inline: 10px;
        box-shadow: unset;
    }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
    .header-area.header_v2:not(.is-sticky) {
        background-color: transparent !important;
        -webkit-backdrop-filter: unset;
        backdrop-filter: unset;
        box-shadow: unset;
    }

    .header-area.header_v2 .main-responsive-nav>.container {
        margin-top: 15px;
    }

    .header-area.header_v2.is-sticky .main-responsive-nav>.container {
        background-color: transparent !important;
        margin-top: 0;
        padding-inline: 10px;
        box-shadow: unset;
    }
}

/* -----------------------------------------
	Header 3 CSS
----------------------------------------- */
.header-area.header_v3 {
    background-color: var(--bg-2);
}

.header-area.header_v3::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: calc(50% - 10px);
    background-color: var(--bg-3);
    z-index: -2;
}

.header-area.header_v3 .navbar-brand {
    position: relative;
    display: flex;
    height: 100%;
    align-items: center;
}

.header-area.header_v3 .navbar-brand::before {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin-inline-end: 0;
    margin-inline-start: auto;
    width: 500px;
    height: 100%;
    background-color: var(--color-primary);
    background-image: var(--gradient-1);
    -webkit-clip-path: polygon(0 0, 100% 0%, 95% 100%, 0% 100%);
    clip-path: polygon(0 0, 100% 0%, 95% 100%, 0% 100%);
    z-index: -1;
}

.header-area.header_v3 .navbar-nav {
    margin: 0;
}

.header-area.header_v3 .header-top {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-block: 5px;
}

.header-area.header_v3 .header-top>div {
    flex: 0 0 auto;
    width: 100%;
}

.header-area.header_v3 .header-top .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: flex-end;
}

.header-area.header_v3 .main-navbar .nav-item:not(.menu-dropdown .nav-item):first-child {
    margin-inline-start: 0;
}

.header-area.header_v3 .main-navbar .nav-item:not(.menu-dropdown .nav-item):first-child>.nav-link {
    padding-inline-start: 0;
}

.header-area.header_v3 .main-navbar .nav-item:not(.menu-dropdown .nav-item)>.nav-link {
    padding-block: 25px;
}

.header-area.header_v3 .header-top,
.header-area.header_v3 .header-bottom {
    padding-inline-start: 30px;
}

.header-area.header_v3.is-sticky::after {
    display: none;
}

.header-area.header_v3.is-sticky .header-top {
    display: none;
}

.header-area.header_v3.is-sticky .social-link {
    --bg: var(--color-primary);
}

@media (max-width: 1199.98px) {
    .header-area.header_v3::after {
        content: unset;
    }

    .header-area.header_v3 .main-responsive-nav {
        background-color: var(--bg-1);
    }

    .header-area.header_v3 .header-top {
        padding: 5px 0;
    }
}

@media (max-width: 767.98px) {
    .header-area.header_v3 .header-top .header-left li {
        font-size: var(--font-sm);
    }
}

@media (max-width: 575.98px) {
    .header-area.header_v3 .header-top {
        padding: 10px 0 0;
    }

    .header-area.header_v3 .header-top .header-left li {
        font-size: var(--font-sm);
    }

    .header-area.header_v3 .header-top .header-right {
        justify-content: center;
        margin-top: 10px;
    }
}

/* -----------------------------------------
	Responsive Header CSS
----------------------------------------- */
@media (max-width: 1199px) {
    .header-area {
        background: rgba(var(--color-white-rgb), 0.9);
        -webkit-backdrop-filter: saturate(180%) blur(20px);
        backdrop-filter: saturate(180%) blur(20px);
        box-shadow: 0px 8px 60px rgba(24, 24, 24, 0.1);
    }

    .header-area.is-sticky {
        position: fixed;
        top: 0;
        width: 100%;
    }

    .header-area .main-responsive-nav {
        display: block;
        height: 60px;
        line-height: 60px;
    }

    .header-area .main-responsive-nav>div {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .header-area .main-navbar {
        display: none;
    }

    .mobile-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 320px;
        height: 100vh;
        overflow: hidden;
        overflow-y: scroll;
        transition: all 0.3s ease-in-out;
        z-index: 999;
        background: var(--bg-white);
    }

    .mobile-menu .mobile-menu-wrapper {
        margin-top: 60px;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }

    .mobile-menu .navbar-nav {
        width: 100%;
        margin-inline-start: 0;
    }

    .mobile-menu .navbar-nav li a {
        padding: 10px 0;
        border-bottom: 1px solid var(--border-color);
        transition: all 700ms ease;
    }

    .mobile-menu .navbar-nav li ul {
        padding-inline-start: 12px;
        list-style-type: none;
    }

    .mobile-menu .navbar-nav li li a {
        font-size: 14px;
    }

    .mobile-menu .more-option {
        order: -1;
        padding-top: 20px;
        padding-bottom: 20px;
        justify-content: center;
    }

    .mobile-menu li.nav-item:not(.show) ul {
        display: none;
    }

    .mobile-menu li.nav-item.show>.toggle {
        position: relative;
    }

    .mobile-menu li.nav-item.show>.toggle i::before {
        content: "\f068";
    }

    .mobile-menu .nav-link.toggle {
        position: relative;
    }

    .mobile-menu .nav-link.toggle i {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
    }

    .mobile-menu .nav-link.toggle:hover i {
        transform: translateY(-50%);
    }

    .mobile-menu-active .mobile-menu {
        visibility: visible;
        left: 0;
    }
}

body.mobile-menu-active {
    overflow: hidden;
}

.menu-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 888;
    width: 100vw;
    height: 100vh;
    background: rgba(var(--color-dark-rgb), 0.5);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    opacity: 1;
    transition: all 0.3s;
}

@media only screen and (min-width: 1200px) {
  .custom-header .main-navbar .container {
    max-width: 1400px;
  }
}
