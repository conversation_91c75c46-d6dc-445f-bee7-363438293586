<?php
header("Content-Type:text/css");

// get the colors from query parameter
$primaryColor = $_GET['primary_color'];
$secondaryColor = $_GET['secondary_color'];
$breadcrumbOverlayColor = $_GET['breadcrumb_overlay_color'];

// check, whether color has '#' or not, will return 0 or 1
function checkColorCode($color)
{
  return preg_match('/^#[a-f0-9]{6}/i', $color);
}

// if, primary color value does not contain '#', then add '#' before color value
if (isset($primaryColor) && (checkColorCode($primaryColor) == 0)) {
  $primaryColor = '#' . $primaryColor;
}

// if, secondary color value does not contain '#', then add '#' before color value
if (isset($secondaryColor) && (checkColorCode($secondaryColor) == 0)) {
  $secondaryColor = '#' . $secondaryColor;
}

// if, breadcrumb overlay color value does not contain '#', then add '#' before color value
if (isset($breadcrumbOverlayColor) && (checkColorCode($breadcrumbOverlayColor) == 0)) {
  $breadcrumbOverlayColor = '#' . $breadcrumbOverlayColor;
}

function rgb($color = null)
  {
      if (!$color) {
          echo '';
      }
      $hex = htmlspecialchars($color);
      [$r, $g, $b] = sscanf($hex, '#%02x%02x%02x');
      echo "$r, $g, $b";
  }

// then add color to style
?>

:root {
  --color-primary: <?php echo htmlspecialchars($primaryColor); ?>;
  --color-secondary: <?php echo htmlspecialchars($secondaryColor); ?>;
  --color-primary-rgb: <?php echo rgb(htmlspecialchars($primaryColor)); ?>;
  --color-secondary-rgb: <?php echo rgb(htmlspecialchars($secondaryColor)); ?>;
}
