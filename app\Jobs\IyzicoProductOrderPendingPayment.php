<?php

namespace App\Jobs;


use App\Models\Earning;
use App\Models\Shop\Product;
use Illuminate\Bus\Queueable;
use App\Models\Shop\ProductOrder;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Session;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Controllers\FrontEnd\Shop\PurchaseProcessController;

class IyzicoProductOrderPendingPayment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $order_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order_id)
    {
        $this->order_id = $order_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $productOrder = ProductOrder::where('id', $this->order_id)->first();
        $conversion_id = $productOrder->conversation_id;

        $options = new \Iyzipay\Options();
        $options->setApiKey(config('Iyzico.api_key'));
        $options->setSecretKey(config('Iyzico.secret_key'));
        $options->setBaseUrl(config('Iyzico.base_url'));

        $request = new \Iyzipay\Request\ReportingPaymentDetailRequest();
        $request->setPaymentConversationId($conversion_id);

        $paymentResponse = \Iyzipay\Model\ReportingPaymentDetail::create($request, $options);

        $result = (array) $paymentResponse;

        foreach ($result as $key => $data) {
            if (is_string($data)) {
                $data = json_decode($data, true);
                if ((isset($data['status']) == 'success') && (count($data['payments'])> 0)) {
                    if (is_array($data['payments'])) {
                        if ($data['payments'][0]['paymentStatus'] == 1) {
                            $purchaseProcess = new PurchaseProcessController();
                            $productOrder->update(['payment_status' => 'completed']);
                            $orderInfo = $productOrder;

                            //add blance to admin revinue
                            $earning = Earning::first();
                            $earning->total_revenue = $earning->total_revenue + $orderInfo->grand_total;
                            $earning->total_earning = $earning->total_earning + $orderInfo->grand_total;
                            $earning->save();

                            $transactionStoreArr = [
                                'transcation_id' => time(),
                                'booking_id' => $orderInfo->id,
                                'transcation_type' => 5,
                                'user_id' => null,
                                'vendor_id' => null,
                                'payment_status' => 1,
                                'payment_method' => $orderInfo->gateway_type,
                                'grand_total' => $orderInfo->grand_total,
                                'pre_balance' => null,
                                'after_balance' => null,
                                'gateway_type' => $orderInfo->gateway_type,
                                'currency_symbol' => $orderInfo->currency_symbol,
                                'currency_symbol_position' => $orderInfo->currency_symbol_position,
                            ];

                            storeTranscation($transactionStoreArr);
                            $productList = Session::get('productCart');
                            // then subtract each product quantity from respective product stock
                            foreach ($productList as $key => $item) {
                                $product = Product::query()->find($key);

                                if ($product->product_type == 'physical') {
                                    $stock = $product->stock - intval($item['quantity']);

                                    $product->update(['stock' => $stock]);
                                }
                            }

                            // generate an invoice in pdf format
                            $invoice = $purchaseProcess->generateInvoice($orderInfo, $productList);

                            // then, update the invoice field info in database
                            $orderInfo->update(['invoice' => $invoice]);

                            // send a mail to the customer with the invoice
                            $purchaseProcess->prepareMail($orderInfo, $transactionStoreArr['transcation_id']);

                            // remove all session data
                            Session::forget('productCart');
                            Session::forget('discount');
                            \Artisan::call('queue:work --stop-when-empty');

                            // return redirect()->route('shop.purchase_product.complete');
                        }
                    }
                }
            }
        }
    }
}
