<?php

namespace App\Models\HomePage;

use App\Models\Language;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class VideoSection extends Model
{
    use HasFactory;

  /**
   * The attributes that are mass assignable.
   *
   * @var array
   */
  protected $fillable = ['language_id', 'title', 'subtitle', 'image', 'link'];

  public function language()
  {
    return $this->belongsTo(Language::class, 'language_id', 'id');
  }

  
}
