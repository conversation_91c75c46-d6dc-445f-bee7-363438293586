/*-----------------------------------------------------------
	* Template Name    : Multirent - Multipurpose / Equipment Rental Website
	* Author           : KreativDev
	* File Description : This file contains the styling for the actual template, this
						is the file you need to edit to change the look of the
						template.
*------------------------------------------------------------*/
/* -----------------------------------------
    Variables CSS
----------------------------------------- */
@import url("home/home-1.css");
@import url("home/home-2.css");
@import url("home/home-3.css");
@import url("home/home-4.css");
@import url("home/home-5.css");
@import url("home/home-6.css");
@import url("home/home-7.css");

/* -----------------------------------------
	Hero Banner CSS
----------------------------------------- */
.hero-banner {
  position: relative;
  z-index: 1;
}
.hero-banner .banner-content {
  max-width: 700px;
}
.hero-banner .banner-content .subtitle {
  display: inline-block;
}
.hero-banner .banner-content .text,
.hero-banner .banner-content p {
  font-size: var(--font-lg);
  max-width: 540px;
  margin-bottom: 0;
}
.hero-banner .banner-filter-form {
  position: relative;
  max-width: 840px;
  z-index: 11;
}
.hero-banner .banner-filter-form .grid {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.hero-banner .banner-filter-form .grid .item {
  flex: 0 0 auto;
  width: calc(25% - 10px);
  padding-inline-start: 15px;
  border-inline-start: 2px solid var(--border-color-2);
}
.hero-banner .banner-filter-form .grid .item:first-child, .hero-banner .banner-filter-form .grid .item:last-child {
  padding: 0;
  border: unset;
}
.hero-banner .banner-filter-form .form-wrapper {
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
}
.hero-banner .banner-filter-form label {
  color: var(--text-medium);
  margin-bottom: 20px;
  line-height: 1;
}
.hero-banner .banner-filter-form .form-block {
  position: relative;
}
.hero-banner .banner-filter-form .form-block .form-control,
.hero-banner .banner-filter-form .form-block select,
.hero-banner .banner-filter-form .form-block .niceselect {
  padding-inline-start: 25px;
}
.hero-banner .banner-filter-form .form-block .icon {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  margin-inline-start: 0;
  margin-inline-end: auto;
  width: 25px;
  font-size: var(--font-sm);
}
.hero-banner .banner-filter-form .niceselect,
.hero-banner .banner-filter-form .form-control {
  padding: 0;
  margin: 0;
  border: 0;
  height: 100%;
  line-height: normal;
  padding-inline-start: 0;
  width: calc(100% - 20px);
  font-size: var(--font-base);
  color: var(--text-medium);
  font-weight: var(--font-normal);
  background: transparent;
  font-size: var(--font-sm);
}
.hero-banner .banner-filter-form .niceselect .list,
.hero-banner .banner-filter-form .form-control .list {
  left: -20px;
  width: 200px;
  color: var(--text-medium);
  border-radius: 3px;
  box-shadow: 0px 0px 30px 0px rgba(var(--color-dark-rgb), 0.15);
}
.hero-banner .banner-filter-form .nice-select .list li,
.hero-banner .banner-filter-form .form-control .list li {
  line-height: 30px;
  min-height: 30px;
}
.hero-banner .banner-filter-form .nice-select::after {
  border-color: var(--text-medium);
  position: relative;
  display: inline-block;
  margin-inline-start: 5px;
  top: -3px;
}
.hero-banner .home-slider {
  position: relative;
  z-index: 2;
}
.hero-banner .home-img-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.hero-banner .home-img-slider .swiper-slide .bg-img{
  background-size: cover;
}

/* -----------------------------------------
    Works CSS
----------------------------------------- */
.works-area {
  position: relative;
  overflow: hidden;
}

.works-area_v1 {
  z-index: 2;
  margin-top: -50px;
}
.works-area_v1 .card {
  margin-top: -1px;
  border-radius: 0;
  transition: all 0.3s;
}
.works-area_v1 .card .card_icon {
  position: relative;
  overflow: hidden;
  width: 80px;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding-top: 20px;
  transition: all 0.4s;
  z-index: 1;
}
.works-area_v1 .card .card_icon::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: calc(100% - 30px);
  background-color: var(--color-primary);
  -webkit-clip-path: polygon(0 0, 100% 0%, 100% 80%, 50% 100%, 0 80%);
          clip-path: polygon(0 0, 100% 0%, 100% 80%, 50% 100%, 0 80%);
  z-index: -1;
}
.works-area_v1 .card .card_icon i {
  font-size: 52px;
  color: var(--text-dark);
}
.works-area_v1 .card:hover {
  border-color: transparent !important;
  box-shadow: var(--shadow-md);
}
.works-area_v1 .card:hover .card_icon {
  width: 100%;
}

.works-area_v2 .card {
  padding: 5px 25px 25px;
  border-radius: 0;
  margin-top: 10px;
  transition: all 0.3s;
}
.works-area_v2 .card::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  bottom: 0;
  height: 5px;
  width: 33.3333333333%;
  margin-inline-start: auto;
  margin-inline-end: 0;
  background-color: var(--color-primary);
  -webkit-clip-path: polygon(10% 0, 100% 0%, 100% 100%, 0% 100%);
          clip-path: polygon(10% 0, 100% 0%, 100% 100%, 0% 100%);
}
.works-area_v2 .card .card_icon {
  --size: 80px;
  position: relative;
  margin-top: 25px;
  padding-top: 5px;
  padding-inline-start: 5px;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  color: var(--color-primary);
  z-index: 1;
}
.works-area_v2 .card .card_icon::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  margin: 5px;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  transform: skewX(-5deg);
  z-index: -1;
}
.works-area_v2 .card .card_icon i {
  font-size: 36px;
}
.works-area_v2 .card .card_number {
  --size: 50px;
  position: absolute;
  top: -25px;
  left: 0;
  right: 0;
  width: var(--size);
  height: var(--size);
  line-height: var(--size);
  text-align: center;
  margin-inline-start: 25px;
  margin-inline-end: auto;
  color: var(--text-dark);
  font-size: var(--font-size--h5);
  font-weight: var(--font-semi-bold);
  background-color: var(--bg-white);
}
.works-area_v2 .card:hover {
  border-color: transparent !important;
  box-shadow: var(--shadow-md);
}
.works-area_v2 .item:nth-child(1) .card::before {
  background-color: #53E7FF;
}
.works-area_v2 .item:nth-child(1) .card_icon {
  color: #53E7FF;
}
.works-area_v2 .item:nth-child(1) .card_icon::before {
  background-color: rgba(83, 231, 255, 0.1);
}
.works-area_v2 .item:nth-child(2) .card::before {
  background-color: #7C4FFF;
}
.works-area_v2 .item:nth-child(2) .card_icon {
  color: #7C4FFF;
}
.works-area_v2 .item:nth-child(2) .card_icon::before {
  background-color: rgba(124, 79, 255, 0.1);
}
.works-area_v2 .item:nth-child(3) .card::before {
  background-color: #FF5397;
}
.works-area_v2 .item:nth-child(3) .card_icon {
  color: #FF5397;
}
.works-area_v2 .item:nth-child(3) .card_icon::before {
  background-color: rgba(255, 83, 151, 0.1);
}
.works-area_v2 .item:nth-child(4) .card::before {
  background-color: #FFB228;
}
.works-area_v2 .item:nth-child(4) .card_icon {
  color: #FFB228;
}
.works-area_v2 .item:nth-child(4) .card_icon::before {
  background-color: rgba(255, 178, 40, 0.1);
}
.works-area_v2 .item:nth-child(5) .card::before {
  background-color: #18D174;
}
.works-area_v2 .item:nth-child(5) .card_icon {
  color: #18D174;
}
.works-area_v2 .item:nth-child(5) .card_icon::before {
  background-color: rgba(24, 209, 116, 0.1);
}
.works-area_v2 .item:nth-child(6) .card::before {
  background-color: #FF60A8;
}
.works-area_v2 .item:nth-child(6) .card_icon {
  color: #FF60A8;
}
.works-area_v2 .item:nth-child(6) .card_icon::before {
  background-color: rgba(255, 96, 168, 0.1);
}
.works-area_v2 .item:nth-child(7) .card::before {
  background-color: #E74C3C;
}
.works-area_v2 .item:nth-child(7) .card_icon {
  color: #E74C3C;
}
.works-area_v2 .item:nth-child(7) .card_icon::before {
  background-color: rgba(231, 76, 60, 0.1);
}
.works-area_v2 .item:nth-child(8) .card::before {
  background-color: #3C8CE7;
}
.works-area_v2 .item:nth-child(8) .card_icon {
  color: #3C8CE7;
}
.works-area_v2 .item:nth-child(8) .card_icon::before {
  background-color: rgba(60, 140, 231, 0.1);
}
.works-area_v2 .item:nth-child(9) .card::before {
  background-color: #FFAA85;
}
.works-area_v2 .item:nth-child(9) .card_icon {
  color: #FFAA85;
}
.works-area_v2 .item:nth-child(9) .card_icon::before {
  background-color: rgba(255, 170, 133, 0.1);
}
.works-area_v2 .item:nth-child(10) .card::before {
  background-color: #3C8CE7;
}
.works-area_v2 .item:nth-child(10) .card_icon {
  color: #3C8CE7;
}
.works-area_v2 .item:nth-child(10) .card_icon::before {
  background-color: rgba(60, 140, 231, 0.1);
}

.works-area_v3 .card {
  border: 1px solid transparent;
  z-index: 1;
}
.works-area_v3 .card::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 40px;
  margin-inline-start: auto;
  margin-inline-end: 25px;
  background-color: var(--bg-3);
  z-index: -1;
}
.works-area_v3 .card .card_icon {
  --size: 60px;
  position: relative;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  color: var(--color-primary);
  z-index: 1;
}
.works-area_v3 .card .card_icon::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 30px;
  height: 100%;
  margin-inline-start: 0;
  margin-inline-end: auto;
  background-color: var(--bg-white);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
  z-index: -1;
}
.works-area_v3 .card .card_icon i {
  font-size: 36px;
}
.works-area_v3 .card .card_icon img {
  max-width: 36px;
}
.works-area_v3 .card .card_number {
  color: transparent;
  webkit-text-fill-color: transparent;
  -webkit-text-stroke-color: var(--text-dark);
  -webkit-text-stroke-width: 1px;
  transition: all 0.3s;
}
.works-area_v3 .card:hover {
  border-color: var(--border-color);
}
.works-area_v3 .card:hover .card_icon::before {
  width: 100%;
}
.works-area_v3 .card:hover .card_number {
  color: var(--text-dark);
}

.works-area_v4 .card {
  background-color: transparent;
}
.works-area_v4 .card_number {
  --size: 60px;
  position: relative;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  background-color: var(--bg-3);
}
.works-area_v4 .card_number * {
  transition: color 0.3s;
}
.works-area_v4 .card_shape {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.works-area_v4 .card_shape svg {
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
  transition: all 0.3s;
}
.works-area_v4 .card_shape svg * {
  fill: var(--bg-2);
  stroke: var(--border-color);
  transition: all 0.3s;
}
.works-area_v4 .card:hover {
  border-color: var(--border-color);
}
.works-area_v4 .card:hover .card_number * {
  color: var(--color-primary);
}
.works-area_v4 .card:hover .card_shape svg * {
  stroke: var(--color-primary);
}

.works-area_v5 .card {
  padding-block: 25px;
  border-radius: 0;
  transition: all 0.6s;
  z-index: 1;
}
.works-area_v5 .card::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  width: 33.3333333333%;
  margin-inline-start: auto;
  margin-inline-end: 0;
  background-color: var(--color-primary);
  -webkit-clip-path: polygon(5% 0, 100% 0%, 100% 100%, 0% 100%);
          clip-path: polygon(5% 0, 100% 0%, 100% 100%, 0% 100%);
  transition: all 0.6s;
}
.works-area_v5 .card::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  width: 5px;
  margin-inline-start: auto;
  margin-inline-end: calc(33.3333333333% + 2px);
  background-color: var(--color-primary);
  transform: skewX(-30deg);
  transition: all 0.6s;
}
.works-area_v5 .card_icon {
  display: flex;
  align-items: center;
  gap: 15px;
  line-height: 1;
  color: var(--color-primary);
}
.works-area_v5 .card_icon i {
  font-size: 36px;
  transition: all 0.6s;
}
.works-area_v5 .card_number {
  padding: 10px 20px;
  color: var(--text-white);
  font-size: var(--font-size--h4);
  font-weight: var(--font-semi-bold);
  background-color: var(--color-primary);
  -webkit-clip-path: polygon(0 0, 100% 0%, 85% 100%, 0% 100%);
          clip-path: polygon(0 0, 100% 0%, 85% 100%, 0% 100%);
  transition: all 0.6s;
}
.works-area_v5 .card_content {
  padding-inline: 20px;
}
.works-area_v5 .card_title {
  transition: all 0.6s;
}
.works-area_v5 .card_shape {
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  width: 100%;
  height: calc(100% - 20px);
  transition: all 0.3s;
  z-index: -1;
}
.works-area_v5 .card_shape svg {
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
}
.works-area_v5 .card_shape svg * {
  fill: rgba(var(--color-white-rgb), 0.2);
  transition: all 0.3s;
}
.works-area_v5 .card:hover {
  background-color: var(--color-primary);
}
.works-area_v5 .card:hover::before, .works-area_v5 .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .card:hover .card_number {
  color: var(--color-primary);
  background-color: var(--bg-white);
}
.works-area_v5 .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(1) .card::before, .works-area_v5 .item:nth-child(1) .card::after {
  background-color: #FFB053;
}
.works-area_v5 .item:nth-child(1) .card .card_icon {
  color: #FFB053;
}
.works-area_v5 .item:nth-child(1) .card .card_number {
  background-color: #FFB053;
}
.works-area_v5 .item:nth-child(1) .card:hover {
  background-color: #FFB053;
}
.works-area_v5 .item:nth-child(1) .card:hover::before, .works-area_v5 .item:nth-child(1) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(1) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(1) .card:hover .card_number {
  color: #FFB053;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(1) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(2) .card::before, .works-area_v5 .item:nth-child(2) .card::after {
  background-color: #8053FF;
}
.works-area_v5 .item:nth-child(2) .card .card_icon {
  color: #8053FF;
}
.works-area_v5 .item:nth-child(2) .card .card_number {
  background-color: #8053FF;
}
.works-area_v5 .item:nth-child(2) .card:hover {
  background-color: #8053FF;
}
.works-area_v5 .item:nth-child(2) .card:hover::before, .works-area_v5 .item:nth-child(2) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(2) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(2) .card:hover .card_number {
  color: #8053FF;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(2) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(3) .card::before, .works-area_v5 .item:nth-child(3) .card::after {
  background-color: #FF7294;
}
.works-area_v5 .item:nth-child(3) .card .card_icon {
  color: #FF7294;
}
.works-area_v5 .item:nth-child(3) .card .card_number {
  background-color: #FF7294;
}
.works-area_v5 .item:nth-child(3) .card:hover {
  background-color: #FF7294;
}
.works-area_v5 .item:nth-child(3) .card:hover::before, .works-area_v5 .item:nth-child(3) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(3) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(3) .card:hover .card_number {
  color: #FF7294;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(3) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(4) .card::before, .works-area_v5 .item:nth-child(4) .card::after {
  background-color: #18D174;
}
.works-area_v5 .item:nth-child(4) .card .card_icon {
  color: #18D174;
}
.works-area_v5 .item:nth-child(4) .card .card_number {
  background-color: #18D174;
}
.works-area_v5 .item:nth-child(4) .card:hover {
  background-color: #18D174;
}
.works-area_v5 .item:nth-child(4) .card:hover::before, .works-area_v5 .item:nth-child(4) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(4) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(4) .card:hover .card_number {
  color: #18D174;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(4) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(5) .card::before, .works-area_v5 .item:nth-child(5) .card::after {
  background-color: #FFB228;
}
.works-area_v5 .item:nth-child(5) .card .card_icon {
  color: #FFB228;
}
.works-area_v5 .item:nth-child(5) .card .card_number {
  background-color: #FFB228;
}
.works-area_v5 .item:nth-child(5) .card:hover {
  background-color: #FFB228;
}
.works-area_v5 .item:nth-child(5) .card:hover::before, .works-area_v5 .item:nth-child(5) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(5) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(5) .card:hover .card_number {
  color: #FFB228;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(5) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(6) .card::before, .works-area_v5 .item:nth-child(6) .card::after {
  background-color: #FF60A8;
}
.works-area_v5 .item:nth-child(6) .card .card_icon {
  color: #FF60A8;
}
.works-area_v5 .item:nth-child(6) .card .card_number {
  background-color: #FF60A8;
}
.works-area_v5 .item:nth-child(6) .card:hover {
  background-color: #FF60A8;
}
.works-area_v5 .item:nth-child(6) .card:hover::before, .works-area_v5 .item:nth-child(6) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(6) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(6) .card:hover .card_number {
  color: #FF60A8;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(6) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(7) .card::before, .works-area_v5 .item:nth-child(7) .card::after {
  background-color: #E74C3C;
}
.works-area_v5 .item:nth-child(7) .card .card_icon {
  color: #E74C3C;
}
.works-area_v5 .item:nth-child(7) .card .card_number {
  background-color: #E74C3C;
}
.works-area_v5 .item:nth-child(7) .card:hover {
  background-color: #E74C3C;
}
.works-area_v5 .item:nth-child(7) .card:hover::before, .works-area_v5 .item:nth-child(7) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(7) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(7) .card:hover .card_number {
  color: #E74C3C;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(7) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(8) .card::before, .works-area_v5 .item:nth-child(8) .card::after {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(8) .card .card_icon {
  color: #3C8CE7;
}
.works-area_v5 .item:nth-child(8) .card .card_number {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(8) .card:hover {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(8) .card:hover::before, .works-area_v5 .item:nth-child(8) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(8) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(8) .card:hover .card_number {
  color: #3C8CE7;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(8) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(9) .card::before, .works-area_v5 .item:nth-child(9) .card::after {
  background-color: #FFAA85;
}
.works-area_v5 .item:nth-child(9) .card .card_icon {
  color: #FFAA85;
}
.works-area_v5 .item:nth-child(9) .card .card_number {
  background-color: #FFAA85;
}
.works-area_v5 .item:nth-child(9) .card:hover {
  background-color: #FFAA85;
}
.works-area_v5 .item:nth-child(9) .card:hover::before, .works-area_v5 .item:nth-child(9) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(9) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(9) .card:hover .card_number {
  color: #FFAA85;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(9) .card:hover .card_title {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(10) .card::before, .works-area_v5 .item:nth-child(10) .card::after {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(10) .card .card_icon {
  color: #3C8CE7;
}
.works-area_v5 .item:nth-child(10) .card .card_number {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(10) .card:hover {
  background-color: #3C8CE7;
}
.works-area_v5 .item:nth-child(10) .card:hover::before, .works-area_v5 .item:nth-child(10) .card:hover::after {
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(10) .card:hover .card_icon {
  color: var(--text-white);
}
.works-area_v5 .item:nth-child(10) .card:hover .card_number {
  color: #3C8CE7;
  background-color: var(--bg-white);
}
.works-area_v5 .item:nth-child(10) .card:hover .card_title {
  color: var(--text-white);
}

/* -----------------------------------------
    Category CSS
----------------------------------------- */
.category-area {
  position: relative;
  overflow: hidden;
}

.category-area_v1 .card {
  z-index: 1;
}
.category-area_v1 .card::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 40px;
  margin-inline: auto;
  background-color: var(--color-primary);
  opacity: 0.1;
  z-index: -1;
}
.category-area_v1 .card .card_icon {
  --size: 70px;
  position: relative;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  z-index: 1;
}
.category-area_v1 .card .card_icon i {
  font-size: 36px;
}
.category-area_v1 .card .card_icon img {
  max-width: var(--size);
}
.category-area_v1 .card:hover {
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.category-area_v2 .card .card_icon {
  --size: 188px;
  position: relative;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  z-index: 1;
  border-radius: var(--radius-circle);
  border: 1px solid rgba(var(--color-primary-rgb), 0.5);
  transition: all 0.3s;
}
.category-area_v2 .card .card_icon::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, -50%);
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  border-radius: inherit;
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  transition: all 0.3s;
}
.category-area_v2 .card .card_icon i {
  font-size: 36px;
}
.category-area_v2 .card .card_icon img {
  max-width: 60px;
}
.category-area_v2 .card:hover .card_icon {
  background-color: var(--color-primary);
}
.category-area_v2 .card:hover .card_icon::before {
  border: 1px solid rgba(var(--color-white-rgb), 1);
}

/* -----------------------------------------
    Choose CSS
----------------------------------------- */
.choose-area {
  position: relative;
  overflow: hidden;
}

.choose-area_v1 .card {
  margin-top: 20px;
  z-index: 1;
  background-color: transparent;
}
.choose-area_v1 .card::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  transform: skewX(-5deg);
  z-index: -1;
}
.choose-area_v1 .card_icon {
  position: relative;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -20px;
  margin-inline-start: 30px;
  padding-inline-start: 10px;
  font-size: 34px;
  color: var(--color-primary);
}
.choose-area_v1 .card_icon img {
  max-width: 45px;
}
.choose-area_v1 .card_icon::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 40px;
  height: 100%;
  margin-inline-start: 0;
  margin-inline-end: auto;
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  transform: skewX(-7deg);
  z-index: -1;
}

.choose-area_v2 .image {
  position: relative;
  padding-inline-end: 50px;
  padding-bottom: 100px;
}
.choose-area_v2 .image .img-1 img {
  -webkit-clip-path: polygon(0 0, 100% 0, 85% 100%, 0% 100%);
          clip-path: polygon(0 0, 100% 0, 85% 100%, 0% 100%);
}
.choose-area_v2 .image .img-2 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: -moz-fit-content;
  width: fit-content;
  margin-inline-start: auto;
  margin-inline-end: 0;
}
.choose-area_v2 .image .img-2::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-dark);
  -webkit-clip-path: polygon(20% 0, 100% 0%, 80% 100%, 0% 100%);
          clip-path: polygon(20% 0, 100% 0%, 80% 100%, 0% 100%);
  opacity: 0.4;
  z-index: 1;
}
.choose-area_v2 .image .img-2 img {
  -webkit-clip-path: polygon(20% 0, 100% 0%, 80% 100%, 0% 100%);
          clip-path: polygon(20% 0, 100% 0%, 80% 100%, 0% 100%);
}
.choose-area_v2 .item-list .card {
  align-items: start;
  flex-direction: unset;
  gap: 15px;
}
.choose-area_v2 .item-list .card_icon {
  position: relative;
  flex: 0 0 auto;
  width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-inline-start: 20px;
  z-index: 1;
}
.choose-area_v2 .item-list .card_icon img {
  max-width: 45px;
}
.choose-area_v2 .item-list .card_icon::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  transform: translateY(-50%);
  width: 66.6666666667%;
  height: 30px;
  margin-inline-start: 0;
  margin-inline-end: auto;
  background-color: rgba(var(--color-primary-rgb), 0.2);
  z-index: -1;
}
.choose-area_v2 .item-list .card:nth-child(1) .card_icon::before {
  background-color: #FFFAD9;
}
.choose-area_v2 .item-list .card:nth-child(2) .card_icon::before {
  background-color: #F0F5FF;
}
.choose-area_v2 .info-list .card {
  z-index: 1;
}
.choose-area_v2 .info-list .card::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  transform: skewX(-5deg);
  z-index: -1;
}

.choose-area_v3 {
  --space: 60px;
}
.choose-area_v3 .content-title {
  padding-block: var(--space);
}
.choose-area_v3 .info-list .card {
  background-color: transparent;
}
.choose-area_v3 .info-list .card_number {
  color: var(--color-primary);
}
.choose-area_v3 .bg-shape svg {
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
  transition: all 0.3s;
}
.choose-area_v3 .bg-shape svg * {
  opacity: 0.45;
  fill: var(--bg-3);
  transition: all 0.3s;
}
.choose-area_v3 .bg-shape svg #main-block {
  opacity: 1;
  fill: var(--bg-2);
}

.choose-area_v4 .image {
  position: relative;
}
.choose-area_v4 .image .about-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-inline-start: 20px;
  margin-inline-end: auto;
  opacity: 0.3;
}
.choose-area_v4 .image .about-text .h1 {
  color: var(--text-white);
  font-size: 5rem;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-color: var(--text-white);
  -webkit-text-stroke-width: 1px;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}
.choose-area_v4 .subtitle, .choose-area_v4 .text {
  color: var(--text-light);
}
.choose-area_v4 .card_title {
  color: var(--text-white);
}
.choose-area_v4 .card_text {
  color: var(--text-light);
}
.choose-area_v4 .info-list {
  --border-color: #303030;
}
.choose-area_v4 .info-list .card {
  z-index: 1;
}
.choose-area_v4 .info-list .card_icon {
  font-size: 45px;
  line-height: 1;
  color: var(--color-primary);
}
.choose-area_v4 .info-list .card_icon img {
  max-width: 45px;
}
.choose-area_v4 .info-list .card::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--border-color);
  background-color: var(--bg-dark-2);
  transform: skewX(-5deg);
  z-index: -1;
}

/* -----------------------------------------
    Product CSS
----------------------------------------- */
.product-default {
  position: relative;
  overflow: hidden;
  background: var(--bg-white);
  transition: all 0.3s ease-out;
}
.product-default .product_img {
  position: relative;
  overflow: hidden;
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.product-default .product_img img {
  transition: transform 0.3s ease-out;
}
.product-default .product_price {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
}
.product-default .product_price .price_text {
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
}
.product-default .product_price .price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 15px;
  padding-inline-start: 30px;
  color: var(--text-white);
  background-color: var(--color-primary);
  -webkit-clip-path: polygon(15% 0, 100% 0%, 100% 100%, 0% 100%);
          clip-path: polygon(15% 0, 100% 0%, 100% 100%, 0% 100%);
}
.product-default .product_price .price .pre-price{
  font-size: var(--font-base);
  text-decoration: line-through;
}
.product-default .product_price .price * {
  color: var(--text-white);
}
@media (max-width: 767.98px) {
  .product-default .product_price .price_text {
    padding-block: 10px;
  }
}
.product-default .pricing_option {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}
.product-default .pricing_option span {
  font-size: var(--font-sm);
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  line-height: 1;
}
.product-default .pricing_option span.active {
  color: var(--text-white);
  border-color: transparent;
  background-color: var(--color-primary);
}
.product-default .product_tag {
  display: inline-block;
}
.product-default .product_tag * {
  color: var(--text-medium);
}
.product-default .product_tag *:hover {
  color: var(--color-primary);
}
.product-default .product_feature_list li:not(:first-child) {
  padding-top: 8px;
  margin-top: 8px;
  border-top: 1px dashed var(--border-color);
}
.product-default .product_feature_list i {
  color: var(--color-green);
}
.product-default .btn-primary {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-medium);
  box-shadow: none;
}
.product-default .show-more {
  transition: color 0.3s;
  display: inline-block;
}
.product-default .show-more:hover {
  color: var(--color-primary);
}
.product-default:hover {
  border-color: transparent !important;
  box-shadow: 0px 40px 30px -10px rgba(95, 95, 95, 0.08);
}
.product-default:hover .product_img img {
  transform: scale3d(1.05, 1.05, 1.05);
}
.product-default:hover .btn {
  color: var(--text-white) !important;
  background: var(--color-primary);
  border-color: var(--color-primary);
}
.product-default:hover .hover-show {
  opacity: 1;
}

.theme-dark .product-default:hover {
  border-color: var(--border-color) !important;
}

.product-default.product-default-2 .product_price {
  justify-content: center;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin-inline: auto;
  z-index: 3;
}
.product-default.product-default-2 .product_price .price {
  padding: 15px;
  padding-inline-start: 15px;
  gap: 10px;
  -webkit-clip-path: unset;
          clip-path: unset;
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}
.product-default.product-default-2 .pricing_option span {
  border-radius: 3px;
}
.product-default.product-default-2:hover {
  border-color: var(--color-primary) !important;
}

.product-default.product-default-3 .product_price {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin-inline-start: auto;
  margin-inline-end: 0;
  width: -moz-fit-content;
  width: fit-content;
  z-index: 1;
}
.product-default.product-default-3 .product_price .price {
  padding: 10px 20px;
  gap: 10px;
  -webkit-clip-path: unset;
          clip-path: unset;
  background-image: var(--gradient-1);
}
.product-default.product-default-3 .product_price .price span {
  color: var(--text-white);
}
.product-default.product-default-3 .product_price .price div {
  color: var(--text-white);
}
.product-default.product-default-3 .pricing_option .active {
  background-image: var(--gradient-1);
}
.product-default.product-default-3:hover .btn {
  border-color: transparent;
  background-image: var(--gradient-1);
}

.product-default.product-default-4 .product_price {
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: -moz-fit-content;
  width: fit-content;
  height: 100%;
  z-index: 1;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}
.product-default.product-default-4 .product_price .price {
  padding: 10px 20px;
  gap: 10px;
  -webkit-clip-path: unset;
          clip-path: unset;
}
.product-default.product-default-4 .product_price .price span {
  color: var(--text-white);
}
.product-default.product-default-4 .product_price .price div {
  color: var(--text-white);
}

.product-default.product-default-5 .product_price {
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
  margin-inline: auto;
  margin-top: -20px;
  z-index: 4;
}
.product-default.product-default-5 .product_price .price {
  padding: 10px 20px;
  gap: 10px;
  -webkit-clip-path: unset;
          clip-path: unset;
}
.product-default.product-default-5 .product_price .price span {
  color: var(--text-white);
}
.product-default.product-default-5 .product_price .price div {
  color: var(--text-white);
}

.product-column .product_price .price {
  justify-content: start;
  padding: 0;
  background-color: transparent;
  -webkit-clip-path: unset;
          clip-path: unset;
  gap: 10px;
}
.product-column .product_price .price span {
  color: var(--text-medium);
}
.product-column .product_price .price div {
  color: var(--color-primary);
}
.product-column .product_feature_list li {
  font-size: var(--font-sm);
}
.product-column .product_feature_list li:not(:first-child) {
  border: unset;
  margin-top: 0;
  padding-top: 5px;
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .product-column .product_feature_list li {
    font-size: var(--font-xsm);
  }
}
.product-column .product_bottom {
  display: flex;
  align-items: center;
  gap: 20px;
}
.product-column .pricing_option {
  justify-content: start;
  gap: 6px;
}
.product-column .pricing_option span {
  font-size: 10px;
  padding: 5px 7px;
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .product-column .pricing_option span {
    font-size: 10px;
  }
}
.product-column_v2 .product_price {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin-inline-start: auto;
  margin-inline-end: 0;
  width: -moz-fit-content;
  width: fit-content;
  background-color: var(--color-primary);
  background-image: var(--gradient-1);
  z-index: 1;
}
.product-column_v2 .product_price .price {
  padding: 10px 20px;
}
.product-column_v2 .product_price .price span {
  color: var(--text-white);
}
.product-column_v2 .product_price .price div {
  color: var(--text-white);
}
.product-column_v2 .pricing_option span.active {
  background-image: var(--gradient-1);
}

/* -----------------------------------------
    Counter CSS
----------------------------------------- */
.counter-area .card {
  position: relative;
  z-index: 1;
  background-color: transparent;
}
.counter-area .card_icon {
  width: 50px;
  height: 50px;
  margin-inline: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 46px;
  z-index: 1;
}
.counter-area .card_icon img {
  max-width: 40px;
}
.counter-area .counter-inner {
  position: relative;
  z-index: 1;
}

.counter-area_v1 .counter-inner {
  margin-top: -100px;
}
.counter-area_v1 .counter-inner .wrapper {
  position: relative;
  padding: 30px 30px 0;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .counter-area_v1 .counter-inner {
    margin-top: -40px;
  }
}

.counter-area_v2 .counter-inner {
  padding: 50px 0 20px;
}

.counter-area_v3 .counter-inner {
  padding: 50px 0 0;
  margin-bottom: 38px;
}
.counter-area_v3 .video-btn {
  bottom: -37px;
  z-index: 2;
}

.counter-area_v4 {
  position: relative;
  overflow: hidden;
}
.counter-area_v4 .counter-inner {
  position: relative;
  -webkit-clip-path: polygon(5% 0, 100% 0, 100% 20%, 100% 85%, 95% 100%, 20% 100%, 0 100%, 0 15%);
          clip-path: polygon(5% 0, 100% 0, 100% 20%, 100% 85%, 95% 100%, 20% 100%, 0 100%, 0 15%);
}
.counter-area_v4 .video-btn {
  position: relative;
}
.counter-area_v4 .big-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-inline: auto;
  opacity: 0.15;
}
.counter-area_v4 .big-text .h1 {
  color: var(--text-white);
  font-size: 6rem;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke-color: var(--text-white);
  -webkit-text-stroke-width: 1px;
}

/* -----------------------------------------
    Banner CSS
----------------------------------------- */
.banner-img {
  position: relative;
}
.banner-img .video-btn {
  z-index: 5;
}

/* -----------------------------------------
    Testimonial CSS
----------------------------------------- */
.testimonial-area {
  position: relative;
  overflow: hidden;
}
.testimonial-area .slider-item {
  position: relative;
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  transition: all 0.4s;
  padding-bottom: 25px;
}
.testimonial-area .slider-item .client {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 25px 30px;
  gap: 15px;
  border-bottom: 1px dashed var(--border-color);
}
.testimonial-area .slider-item .quote {
  position: relative;
  padding: 0 30px;
}
.testimonial-area .slider-item .quote::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 0;
  right: 0;
  margin-inline-start: 15px;
  margin-inline-end: auto;
  width: 3px;
  height: 100%;
  background: var(--color-primary);
  transform: translateY(-50%);
}
.testimonial-area .slider-item .icon {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  width: fit-content;
  margin-inline-end: 20px;
  margin-inline-start: auto;
  font-size: 62px;
  line-height: 1;
  color: rgba(var(--color-primary-rgb), 0.1);
}
.testimonial-area .slider-item p {
  margin-bottom: 0;
}
.testimonial-area .slider-item .client-info h6 {
  margin-bottom: 0;
}
.testimonial-area .slider-item .client-info .designation {
  font-size: var(--font-sm);
}
.testimonial-area .slider-item .client-img {
  max-width: 50px;
  margin-inline-end: 16px;
}

.testimonial-area_v1 .img-content, .testimonial-area_v2 .img-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin-inline-start: auto;
  max-width: 50%;
}
.testimonial-area_v1 .img-content img, .testimonial-area_v2 .img-content img {
  display: block;
  margin-inline: auto;
}
.testimonial-area_v1 .slider-item, .testimonial-area_v2 .slider-item {
  box-shadow: var(--shadow-md);
  border-color: transparent;
}
.testimonial-area_v1 .swiper-slide.swiper-slide-active .slider-item, .testimonial-area_v2 .swiper-slide.swiper-slide-active .slider-item {
  box-shadow: unset;
  border-color: var(--border-color);
}

.testimonial-area_v2 .img-content img {
  -webkit-clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
          clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
}

.testimonial-area_v3 .swiper-slide {
  height: 50% !important;
}
.testimonial-area_v3 .swiper-grid-column > .swiper-wrapper {
  flex-direction: row;
  row-gap: 30px;
}
@media (max-width: 991.98px) {
  .testimonial-area_v3 .swiper-wrapper.row {
    flex-wrap: nowrap;
    --bs-gutter-x: 0;
    margin-inline: 0;
  }
  .testimonial-area_v3 .swiper-slide {
    height: auto !important;
  }
}

.testimonial-area_v5 .images .img {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
}
.testimonial-area_v5 .images .img.img-1 {
  left: 0;
}
.testimonial-area_v5 .images .img.img-2 {
  right: 0;
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .testimonial-area_v5 .images .img {
    width: 25%;
  }
}

/* -----------------------------------------
	Blog CSS
----------------------------------------- */
.blog-area {
  position: relative;
  overflow: hidden;
}
.blog-area .card {
  overflow: hidden;
  transition: all 0.3s;
}
.blog-area .card_top {
  position: relative;
}
.blog-area .card_img {
  overflow: hidden;
  border-radius: 0;
}
.blog-area .card img {
  transition: transform 0.3s ease-out;
}
.blog-area .card p {
  margin-bottom: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.blog-area .card_list {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: var(--font-sm);
}
.blog-area .card_list a {
  font-size: var(--font-xsm);
  color: var(--text-medium);
}
.blog-area .card_list i {
  color: var(--color-primary);
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .blog-area .card_list {
    gap: 15px;
    font-size: var(--font-sm) !important;
  }
}
.blog-area .card .tag {
  display: inline-block;
  padding: 5px 10px;
  font-size: var(--font-sm);
  border: 1px solid var(--border-color);
}
.blog-area .card:hover img {
  transform: scale3d(1.05, 1.05, 1.05);
}
.blog-area .card:hover .btn-text {
  letter-spacing: 0.1em;
}

.blog-area_v1 .card {
  background-color: transparent;
}
.blog-area_v1 .card_list {
  position: relative;
  margin-top: -30px;
  padding: 15px;
  width: -moz-fit-content;
  width: 100%;
  justify-content: center;
  margin-inline: auto;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
  z-index: 1;
}

.blog-area_v2 .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 50%;
  opacity: 1;
}
.blog-area_v2 .overlay::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-dark);
  opacity: 0.8;
}
.blog-area_v2 .card_img {
  padding: 10px 10px 0;
}
.blog-area_v2 .card_list {
  position: relative;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
  font-size: var(--font-base);
  z-index: 1;
}

.blog-area_v3 .card_top {
  padding: 10px 10px 0;
}
.blog-area_v3 .card_list {
  position: relative;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
  font-size: var(--font-base);
  margin-top: -30px;
  z-index: 1;
}

.blog-area_v4 .card .btn {
  background-color: var(--bg-3);
  border: unset;
  color: var(--text-dark);
}

.blog-area_v5 .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 50%;
  opacity: 1;
  -webkit-clip-path: polygon(5% 0, 95% 0, 100% 12%, 100% 80%, 100% 100%, 0 100%, 0% 80%, 0 12%);
          clip-path: polygon(5% 0, 95% 0, 100% 12%, 100% 80%, 100% 100%, 0 100%, 0% 80%, 0 12%);
}
.blog-area_v5 .overlay::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-dark);
  opacity: 0.8;
  -webkit-clip-path: polygon(5% 0, 95% 0, 100% 12%, 100% 80%, 100% 100%, 0 100%, 0% 80%, 0 12%);
          clip-path: polygon(5% 0, 95% 0, 100% 12%, 100% 80%, 100% 100%, 0 100%, 0% 80%, 0 12%);
}
.blog-area_v5 .card_img {
  position: relative;
}
.blog-area_v5 .card_img::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -3.77%, #000 99.97%);
  z-index: 3;
}
.blog-area_v5 .card_content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: end;
  flex-direction: column;
  z-index: 4;
}
.blog-area_v5 .card_list a {
  color: var(--text-white);
}

.cart-wrapper {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin-inline-start: auto;
  margin-inline-end: 0;
  width: fit-content;
  transform: translateY(-50%);
  z-index: 9;
}

.cart-wrapper .btn-icon {
  width: auto;
  height: auto;
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  line-height: 1;
  background-color: #242323;
}
.cart-wrapper .btn-icon span{
  font-size: 14px;
}
