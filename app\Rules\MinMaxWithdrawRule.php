<?php

namespace App\Rules;

use App\Models\WithdrawPaymentMethod;
use Illuminate\Contracts\Validation\Rule;

class MinMaxWithdrawRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return [$attribute, $value];
        $method = WithdrawPaymentMethod::find($value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The validation error message.';
    }
}
