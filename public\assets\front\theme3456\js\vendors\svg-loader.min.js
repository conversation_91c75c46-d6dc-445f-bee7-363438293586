(()=>{"use strict";var t={701:t=>{let e=0;t.exports={incr:()=>++e,decr:()=>--e,curr:()=>e}},941:t=>{t.exports=(t,e,r="")=>{const n=/url\(['"]?#([\w:.-]+)['"]?\)/g,a=/#([\w:.-]+)/g;return e.match(n)&&(e=e.replace(n,(function(e,r){return t[r]?`url(#${t[r]})`:e}))),["href","xlink:href"].includes(r)&&e.match(a)&&(e=e.replace(a,(function(e,r){return t[r]?`#${t[r]}`:e}))),e}},905:t=>{t.exports=(t,e,r)=>{const n=new RegExp("([^\r\n,{}]+)(,(?=[^}]*{)|s*{)","g");return t.replace(n,(function(t,n,a){if(n.match(/^\s*(@media|@.*keyframes|to|from|@font-face|1?[0-9]?[0-9])/))return n+a;const o=n.match(/#(\w+)/);return o&&r[o[1]]&&(n=n.replace(o[0],`#${r[o[1]]}`)),(n=n.replace(/^(\s*)/,"$1"+e+" "))+a}))}},678:(t,e,r)=>{function n(t){return new Promise(((e,r)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>r(t.error)}))}function a(t,e){const r=indexedDB.open(t);r.onupgradeneeded=()=>r.result.createObjectStore(e);const a=n(r);return(t,r)=>a.then((n=>r(n.transaction(e,t).objectStore(e))))}let o;function s(){return o||(o=a("keyval-store","keyval")),o}function i(t,e=s()){return e("readonly",(e=>n(e.get(t))))}function c(t,e,r=s()){return r("readwrite",(r=>(r.put(e,t),n(r.transaction))))}function u(t,e=s()){return e("readwrite",(e=>(t.forEach((t=>e.put(t[1],t[0]))),n(e.transaction))))}function l(t,e=s()){return e("readonly",(e=>Promise.all(t.map((t=>n(e.get(t)))))))}function d(t,e,r=s()){return r("readwrite",(r=>new Promise(((a,o)=>{r.get(t).onsuccess=function(){try{r.put(e(this.result),t),a(n(r.transaction))}catch(t){o(t)}}}))))}function f(t,e=s()){return e("readwrite",(e=>(e.delete(t),n(e.transaction))))}function h(t,e=s()){return e("readwrite",(e=>(t.forEach((t=>e.delete(t))),n(e.transaction))))}function b(t=s()){return t("readwrite",(t=>(t.clear(),n(t.transaction))))}function g(t,e){return t.openCursor().onsuccess=function(){this.result&&(e(this.result),this.result.continue())},n(t.transaction)}function y(t=s()){return t("readonly",(t=>{if(t.getAllKeys)return n(t.getAllKeys());const e=[];return g(t,(t=>e.push(t.key))).then((()=>e))}))}function p(t=s()){return t("readonly",(t=>{if(t.getAll)return n(t.getAll());const e=[];return g(t,(t=>e.push(t.value))).then((()=>e))}))}function m(t=s()){return t("readonly",(e=>{if(e.getAll&&e.getAllKeys)return Promise.all([n(e.getAllKeys()),n(e.getAll())]).then((([t,e])=>t.map(((t,r)=>[t,e[r]]))));const r=[];return t("readonly",(t=>g(t,(t=>r.push([t.key,t.value]))).then((()=>r))))}))}r.r(e),r.d(e,{clear:()=>b,createStore:()=>a,del:()=>f,delMany:()=>h,entries:()=>m,get:()=>i,getMany:()=>l,keys:()=>y,promisifyRequest:()=>n,set:()=>c,setMany:()=>u,update:()=>d,values:()=>p})}},e={};function r(n){var a=e[n];if(void 0!==a)return a.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{const{get:t,set:e,del:n,entries:a}=r(678),o=r(905),s=r(941),i=r(701),c=[],u={},l=(t,e,r)=>{const{enableJs:n,disableUniqueIds:a,disableCssScoping:l}=e,d=(new DOMParser).parseFromString(r,"text/html"),f=d.querySelector("svg"),h=(()=>{if(c.length)return c;for(const t in document.head)t.startsWith("on")&&c.push(t);return c})(),b=u[t.getAttribute("data-id")]||new Set,g=t.getAttribute("data-id")||`svg-loader_${i.incr()}`,y={};a||Array.from(d.querySelectorAll("[id]")).forEach((t=>{const e=t.getAttribute("id"),r=`${e}_${i.incr()}`;t.setAttribute("id",r),y[e]=r})),Array.from(d.querySelectorAll("*")).forEach((e=>{if("script"===e.tagName){if(e.remove(),!n)return;{const r=document.createElement("script");r.appendChild(e.childNodes[0]),t.appendChild(r)}}const r=[];for(let t=0;t<e.attributes.length;t++){const{name:a,value:o}=e.attributes[t],i=s(y,o,a);o!==i&&e.setAttribute(a,i),!h.includes(a.toLowerCase())||n?["href","xlink:href"].includes(a)&&o.startsWith("javascript")&&!n&&r.push(a):r.push(a)}if(r.forEach((t=>e.removeAttribute(t))),"style"===e.tagName&&!l){let t=o(e.innerHTML,`[data-id="${g}"]`,y);t=s(y,t),t!==e.innerHTML&&(e.innerHTML=t)}}));for(let e=0;e<f.attributes.length;e++){const{name:r,value:n}=f.attributes[e];t.getAttribute(r)&&!b.has(r)||(b.add(r),t.setAttribute(r,n))}u[g]=b,t.setAttribute("data-id",g),t.innerHTML=f.innerHTML;const p=new CustomEvent("iconload",{bubbles:!0});if(t.dispatchEvent(p),t.getAttribute("oniconload")){t.setAttribute("onauxclick",t.getAttribute("oniconload"));const e=new CustomEvent("auxclick",{bubbles:!1,view:window});t.dispatchEvent(e),t.removeAttribute("onauxclick")}},d={},f={},h=async r=>{const a=r.getAttribute("data-src"),o=r.getAttribute("data-cache"),s="enabled"===r.getAttribute("data-js"),i="disabled"===r.getAttribute("data-unique-ids"),c="disabled"===r.getAttribute("data-css-scoping"),u=await(async e=>{let r;try{r=await t(`loader_${e}`)}catch(t){}if(!r)try{r=localStorage.getItem(`loader_${e}`)}catch(t){}if(r)return r=JSON.parse(r),Date.now()<r.expiry?r.data:void n(`loader_${e}`)})(a),b="disabled"!==o,g=l.bind(self,r,{enableJs:s,disableUniqueIds:i,disableCssScoping:c});if(f[a]||b&&u){const t=f[a]||u;g(t)}else{if(d[a])return void setTimeout((()=>h(r)),20);d[a]=!0,fetch(a).then((t=>{if(!t.ok)throw Error(`Request for '${a}' returned ${t.status} (${t.statusText})`);return t.text()})).then((t=>{const r=t.toLowerCase().trim();if(!r.startsWith("<svg")&&!r.startsWith("<?xml"))throw Error(`Resource '${a}' returned an invalid SVG file`);b&&(async(t,r,n)=>{const a=parseInt(n,10),o=JSON.stringify({data:r,expiry:Date.now()+(Number.isNaN(a)?2592e6:a)});try{await e(`loader_${t}`,o)}catch(e){try{localStorage.setItem(`loader_${t}`,o)}catch(t){console.warn("Failed to set cache: ",t)}}})(a,t,o),f[a]=t,g(t)})).catch((t=>{console.error(t)})).finally((()=>{delete d[a]}))}};if(globalThis.IntersectionObserver){const p=new IntersectionObserver((t=>{t.forEach((t=>{t.isIntersecting&&(h(t.target),p.unobserve(t.target))}))}),{rootMargin:"1200px"})}const b=[];function g(){Array.from(document.querySelectorAll("svg[data-src]:not([data-id])")).forEach((t=>{-1===b.indexOf(t)&&(b.push(t),"lazy"===t.getAttribute("data-loading")?(void 0).observe(t):h(t))}))}let y=!1;if(globalThis.addEventListener){const m=setInterval((()=>{g()}),100);function v(){clearInterval(m),g(),y||(y=!0,new MutationObserver((t=>{t.some((t=>Array.from(t.addedNodes).some((t=>t.nodeType===Node.ELEMENT_NODE&&(t.getAttribute("data-src")&&!t.getAttribute("data-id")||t.querySelector("svg[data-src]:not([data-id])"))))))&&g(),t.forEach((t=>{"attributes"===t.type&&h(t.target)}))})).observe(document.documentElement,{attributeFilter:["data-src"],attributes:!0,childList:!0,subtree:!0}))}"interactive"===document.readyState?v():globalThis.addEventListener("DOMContentLoaded",(()=>{v()}))}globalThis.SVGLoader={},globalThis.SVGLoader.destroyCache=async()=>{try{const t=await a();for(const e of t)e[0].startsWith("loader_")&&await n(e[0])}catch(t){}Object.keys(localStorage).forEach((t=>{t.startsWith("loader_")&&localStorage.removeItem(t)}))}})()})();
//# sourceMappingURL=svg-loader.min.js.map