<div class="sidebar sidebar-style-2"
    data-background-color="<?php echo e($settings->admin_theme_version == 'light' ? 'white' : 'dark2'); ?>">
    <div class="sidebar-wrapper scrollbar scrollbar-inner">
        <div class="sidebar-content">
            <div class="user">
                <div class="avatar-sm float-left mr-2">
                    <?php if(Auth::guard('admin')->user()->image != null): ?>
                        <img src="<?php echo e(asset('assets/img/admins/' . Auth::guard('admin')->user()->image)); ?>"
                            alt="Admin Image" class="avatar-img rounded-circle">
                    <?php else: ?>
                        <img src="<?php echo e(asset('assets/img/blank_user.jpg')); ?>" alt=""
                            class="avatar-img rounded-circle">
                    <?php endif; ?>
                </div>

                <div class="info">
                    <a data-toggle="collapse" href="#adminProfileMenu" aria-expanded="true">
                        <span>
                            <?php echo e(Auth::guard('admin')->user()->first_name); ?>


                            <?php if(is_null($roleInfo)): ?>
                                <span class="user-level"><?php echo e(__('Super Admin')); ?>

                                </span>
                            <?php else: ?>
                                <span class="user-level"><?php echo e($roleInfo->name); ?></span>
                            <?php endif; ?>

                            <span class="caret"></span>
                        </span>
                    </a>

                    <div class="clearfix"></div>

                    <div class="collapse in" id="adminProfileMenu">
                        <ul class="nav">
                            <li>
                                <a href="<?php echo e(route('admin.edit_profile')); ?>">
                                    <span class="link-collapse"><?php echo e('Edit Profile'); ?></span>
                                </a>
                            </li>

                            <li>
                                <a href="<?php echo e(route('admin.change_password')); ?>">
                                    <span class="link-collapse"><?php echo e('Change Password'); ?></span>
                                </a>
                            </li>

                            <li>
                                <a href="<?php echo e(route('admin.logout')); ?>">
                                    <span class="link-collapse"><?php echo e('Logout'); ?></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <?php
                if (!is_null($roleInfo)) {
                    $rolePermissions = json_decode($roleInfo->permissions);
                }
            ?>

            <ul class="nav nav-primary">
                
                <div class="row mb-3">
                    <div class="col-12">
                        <form action="">
                            <div class="form-group py-0">
                                <input name="term" type="text" class="form-control sidebar-search ltr"
                                    placeholder="Search Menu Here...">
                            </div>
                        </form>
                    </div>
                </div>

                
                <li class="nav-item <?php if(request()->routeIs('admin.dashboard')): ?> active <?php endif; ?>">
                    <a href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="la flaticon-paint-palette"></i>
                        <p><?php echo e('Dashboard'); ?></p>
                    </a>
                </li>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Menu Builder', $rolePermissions))): ?>
                    <li class="nav-item <?php if(request()->routeIs('admin.menu_builder')): ?> active <?php endif; ?>">
                        <a href="<?php echo e(route('admin.menu_builder', ['language' => $defaultLang->code])); ?>">
                            <i class="fal fa-bars"></i>
                            <p><?php echo e('Menu Builder'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Equipment', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.equipment_management.categories')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_management.create_equipment')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_management.all_equipment')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_management.create_equipment')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_management.edit_equipment')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#equipment">
                            <i class="fal fa-truck-container"></i>
                            <p><?php echo e('Equipment'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="equipment"
                            class="collapse 
              <?php if(request()->routeIs('admin.equipment_management.categories')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_management.create_equipment')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_management.all_equipment')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_management.create_equipment')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_management.edit_equipment')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.equipment_management.categories') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.equipment_management.categories', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Categories'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.equipment_management.create_equipment') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.equipment_management.create_equipment')); ?>">
                                        <span class="sub-item"><?php echo e('Add Equipment'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php if(request()->routeIs('admin.equipment_management.all_equipment')): ?> active 
                  <?php elseif(request()->routeIs('admin.equipment_management.edit_equipment')): ?> active <?php endif; ?>">
                                    <a
                                        href="<?php echo e(route('admin.equipment_management.all_equipment', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('All Equipment'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Equipment Booking', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.equipment_booking.settings.tax_amount')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.settings.coupons')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.settings.shipping_methods')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.settings.locations')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.settings.guest_checkout_status')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.bookings')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.details')): ?> active 
            <?php elseif(request()->routeIs('admin.equipment_booking.report')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#equipment-booking">
                            <i class="fal fa-calendar-alt"></i>
                            <p><?php echo e('Equipment Booking'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="equipment-booking"
                            class="collapse 
              <?php if(request()->routeIs('admin.equipment_booking.settings.tax_amount')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.settings.coupons')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.settings.shipping_methods')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.settings.locations')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.settings.guest_checkout_status')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.bookings')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.details')): ?> show 
              <?php elseif(request()->routeIs('admin.equipment_booking.report')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="submenu">
                                    <a data-toggle="collapse" href="#booking-settings">
                                        <span class="sub-item"><?php echo e('Settings'); ?></span>
                                        <span class="caret"></span>
                                    </a>

                                    <div id="booking-settings"
                                        class="collapse 
                    <?php if(request()->routeIs('admin.equipment_booking.settings.tax_amount')): ?> show 
                    <?php elseif(request()->routeIs('admin.equipment_booking.settings.coupons')): ?> show 
                    <?php elseif(request()->routeIs('admin.equipment_booking.settings.shipping_methods')): ?> show 
                    <?php elseif(request()->routeIs('admin.equipment_booking.settings.locations')): ?> show 
                    <?php elseif(request()->routeIs('admin.equipment_booking.settings.guest_checkout_status')): ?> show <?php endif; ?>">
                                        <ul class="nav nav-collapse subnav">
                                            <li
                                                class="<?php echo e(request()->routeIs('admin.equipment_booking.settings.tax_amount') ? 'active' : ''); ?>">
                                                <a href="<?php echo e(route('admin.equipment_booking.settings.tax_amount')); ?>">
                                                    <span class="sub-item"><?php echo e('Tax & Commission'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php echo e(request()->routeIs('admin.equipment_booking.settings.coupons') ? 'active' : ''); ?>">
                                                <a href="<?php echo e(route('admin.equipment_booking.settings.coupons')); ?>">
                                                    <span class="sub-item"><?php echo e('Coupons'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php echo e(request()->routeIs('admin.equipment_booking.settings.shipping_methods') ? 'active' : ''); ?>">
                                                <a
                                                    href="<?php echo e(route('admin.equipment_booking.settings.shipping_methods')); ?>">
                                                    <span class="sub-item"><?php echo e('Shipping Methods'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php echo e(request()->routeIs('admin.equipment_booking.settings.locations') ? 'active' : ''); ?>">
                                                <a
                                                    href="<?php echo e(route('admin.equipment_booking.settings.locations', ['language' => $defaultLang->code])); ?>">
                                                    <span class="sub-item"><?php echo e('Locations'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php echo e(request()->routeIs('admin.equipment_booking.settings.guest_checkout_status') ? 'active' : ''); ?>">
                                                <a
                                                    href="<?php echo e(route('admin.equipment_booking.settings.guest_checkout_status')); ?>">
                                                    <span class="sub-item"><?php echo e('Guest Checkout'); ?></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>

                                <li
                                    class="<?php if(request()->routeIs('admin.equipment_booking.bookings')): ?> active 
                  <?php elseif(request()->routeIs('admin.equipment_booking.details')): ?> active <?php endif; ?>">
                                    <a
                                        href="<?php echo e(route('admin.equipment_booking.bookings', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Bookings'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.equipment_booking.report') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.equipment_booking.report')); ?>">
                                        <span class="sub-item"><?php echo e('Report'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                <li class="nav-item <?php if(request()->routeIs('admin.security-deposit-refunds')): ?> active <?php endif; ?>">
                    <a href="<?php echo e(route('admin.security-deposit-refunds')); ?>">
                        <i class="fal fa-undo"></i>
                        <p><?php echo e('Security Deposit Refunds'); ?></p>
                    </a>
                </li>

                <li class="nav-item <?php if(request()->routeIs('admin.security-deposit.dispute-request')): ?> active <?php endif; ?>">
                    <a href="<?php echo e(route('admin.security-deposit.dispute-request')); ?>">
                        <i class="fal fa-gavel"></i>
                        <p><?php echo e('Dispute Request'); ?></p>
                    </a>
                </li>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Shop Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.shop_management.tax_amount')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.shipping_charges')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.coupons')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.product.categories')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.products')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.select_product_type')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.create_product')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.edit_product')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.orders')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.order.details')): ?> active 
            <?php elseif(request()->routeIs('admin.shop_management.report')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#shop">
                            <i class="fal fa-store-alt"></i>
                            <p><?php echo e('Shop Management'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="shop"
                            class="collapse 
              <?php if(request()->routeIs('admin.shop_management.tax_amount')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.shipping_charges')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.coupons')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.product.categories')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.products')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.select_product_type')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.create_product')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.edit_product')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.orders')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.order.details')): ?> show 
              <?php elseif(request()->routeIs('admin.shop_management.report')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.shop_management.tax_amount') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.shop_management.tax_amount')); ?>">
                                        <span class="sub-item"><?php echo e('Tax Amount'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.shop_management.shipping_charges') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.shop_management.shipping_charges', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Shipping Charges'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.shop_management.coupons') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.shop_management.coupons')); ?>">
                                        <span class="sub-item"><?php echo e('Coupons'); ?></span>
                                    </a>
                                </li>

                                <li class="submenu">
                                    <a data-toggle="collapse" href="#product">
                                        <span class="sub-item"><?php echo e('Manage Products'); ?></span>
                                        <span class="caret"></span>
                                    </a>

                                    <div id="product"
                                        class="collapse 
                    <?php if(request()->routeIs('admin.shop_management.product.categories')): ?> show 
                    <?php elseif(request()->routeIs('admin.shop_management.products')): ?> show 
                    <?php elseif(request()->routeIs('admin.shop_management.select_product_type')): ?> show 
                    <?php elseif(request()->routeIs('admin.shop_management.create_product')): ?> show 
                    <?php elseif(request()->routeIs('admin.shop_management.edit_product')): ?> show <?php endif; ?>">
                                        <ul class="nav nav-collapse subnav">
                                            <li
                                                class="<?php echo e(request()->routeIs('admin.shop_management.product.categories') ? 'active' : ''); ?>">
                                                <a
                                                    href="<?php echo e(route('admin.shop_management.product.categories', ['language' => $defaultLang->code])); ?>">
                                                    <span class="sub-item"><?php echo e('Categories'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php if(request()->routeIs('admin.shop_management.products')): ?> active 
                        <?php elseif(request()->routeIs('admin.shop_management.select_product_type')): ?> active 
                        <?php elseif(request()->routeIs('admin.shop_management.create_product')): ?> active 
                        <?php elseif(request()->routeIs('admin.shop_management.edit_product')): ?> active <?php endif; ?>">
                                                <a
                                                    href="<?php echo e(route('admin.shop_management.products', ['language' => $defaultLang->code])); ?>">
                                                    <span class="sub-item"><?php echo e('Products'); ?></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>

                                <li
                                    class="<?php if(request()->routeIs('admin.shop_management.orders')): ?> active 
                  <?php elseif(request()->routeIs('admin.shop_management.order.details')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.shop_management.orders')); ?>">
                                        <span class="sub-item"><?php echo e('Orders'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.shop_management.report') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.shop_management.report')); ?>">
                                        <span class="sub-item"><?php echo e('Report'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('User Management', $rolePermissions))): ?>
                    
                    <li
                        class="nav-item
          <?php if(request()->routeIs('admin.user_management.registered_users')): ?> active 
              <?php elseif(request()->routeIs('admin.user_management.user.details')): ?> active 
              <?php elseif(request()->routeIs('admin.user_management.user.change_password')): ?> active <?php endif; ?>
          ">
                        <a href="<?php echo e(route('admin.user_management.registered_users')); ?>">
                            <i class="fal fa-users"></i>
                            <p><?php echo e('Customers Management'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Vendor Mangement', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.vendor_management.registered_vendor')): ?> active
            <?php elseif(request()->routeIs('admin.vendor_management.add_vendor')): ?> active
            <?php elseif(request()->routeIs('admin.vendor_management.vendor_details')): ?> active
            <?php elseif(request()->routeIs('admin.edit_management.vendor_edit')): ?> active
            <?php elseif(request()->routeIs('admin.vendor_management.settings')): ?> active
            <?php elseif(request()->routeIs('admin.vendor_management.vendor.change_password')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#vendor">
                            <i class="fal fa-users-crown"></i>
                            <p><?php echo e('Vendors Management'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="vendor"
                            class="collapse
              <?php if(request()->routeIs('admin.vendor_management.registered_vendor')): ?> show
              <?php elseif(request()->routeIs('admin.vendor_management.vendor_details')): ?> show
              <?php elseif(request()->routeIs('admin.edit_management.vendor_edit')): ?> show
              <?php elseif(request()->routeIs('admin.vendor_management.add_vendor')): ?> show
              <?php elseif(request()->routeIs('admin.vendor_management.settings')): ?> show
              <?php elseif(request()->routeIs('admin.vendor_management.vendor.change_password')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="<?php if(request()->routeIs('admin.vendor_management.settings')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.vendor_management.settings')); ?>">
                                        <span class="sub-item"><?php echo e('Settings'); ?></span>
                                    </a>
                                </li>
                                <li
                                    class="<?php if(request()->routeIs('admin.vendor_management.registered_vendor')): ?> active
                  <?php elseif(request()->routeIs('admin.vendor_management.vendor_details')): ?> active
                  <?php elseif(request()->routeIs('admin.edit_management.vendor_edit')): ?> active
                  <?php elseif(request()->routeIs('admin.vendor_management.vendor.change_password')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.vendor_management.registered_vendor')); ?>">
                                        <span class="sub-item"><?php echo e('Registered vendors'); ?></span>
                                    </a>
                                </li>
                                <li class="<?php if(request()->routeIs('admin.vendor_management.add_vendor')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.vendor_management.add_vendor')); ?>">
                                        <span class="sub-item"><?php echo e('Add vendor'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <li
                    class="nav-item <?php if(request()->routeIs('admin.user_management.subscribers')): ?> active 
                  <?php elseif(request()->routeIs('admin.user_management.mail_for_subscribers')): ?> active <?php endif; ?>">
                    <a href="<?php echo e(route('admin.user_management.subscribers')); ?>">
                        <i class="fal fa-user-clock"></i>
                        <p><?php echo e('Subscribers'); ?></p>
                    </a>
                </li>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('User Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.user_management.push_notification.settings')): ?> active 
                    <?php elseif(request()->routeIs('admin.user_management.push_notification.notification_for_visitors')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#push_notification">
                            <i class="fal fa-bell"></i>
                            <p><?php echo e('Push Notification'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="push_notification"
                            class="collapse 
              <?php if(request()->routeIs('admin.user_management.push_notification.settings')): ?> show 
                    <?php elseif(request()->routeIs('admin.user_management.push_notification.notification_for_visitors')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.user_management.push_notification.settings') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.user_management.push_notification.settings')); ?>">
                                        <span class="sub-item"><?php echo e('Settings'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.user_management.push_notification.notification_for_visitors') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.user_management.push_notification.notification_for_visitors')); ?>">
                                        <span class="sub-item"><?php echo e('Send Notification'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>


                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Withdraw Method', $rolePermissions))): ?>
                    <li
                        class="nav-item
          <?php if(request()->routeIs('admin.withdraw.payment_method')): ?> active
          <?php elseif(request()->routeIs('admin.withdraw.payment_method')): ?> active
          <?php elseif(request()->routeIs('admin.withdraw_payment_method.mange_input')): ?> active
          <?php elseif(request()->routeIs('admin.withdraw_payment_method.edit_input')): ?> active
          <?php elseif(request()->routeIs('admin.withdraw.withdraw_request')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#withdraw_method">
                            <i class="fal fa-credit-card"></i>
                            <p><?php echo e('Withdraw'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="withdraw_method"
                            class="collapse
            <?php if(request()->routeIs('admin.withdraw.payment_method')): ?> show
            <?php elseif(request()->routeIs('admin.withdraw.payment_method')): ?> show
            <?php elseif(request()->routeIs('admin.withdraw_payment_method.mange_input')): ?> show
            <?php elseif(request()->routeIs('admin.withdraw_payment_method.edit_input')): ?> show
            <?php elseif(request()->routeIs('admin.withdraw.withdraw_request')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.withdraw.payment_method') && empty(request()->input('status')) ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.withdraw.payment_method', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Payment Methods'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.withdraw.withdraw_request') && empty(request()->input('status')) ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.withdraw.withdraw_request', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Withdraw Requests'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Announcement Popups', $rolePermissions))): ?>
                    <li class="nav-item <?php if(request()->routeIs('admin.transcation')): ?> active <?php endif; ?>">
                        <a href="<?php echo e(route('admin.transcation')); ?>">
                            <i class="fal fa-exchange-alt"></i>
                            <p><?php echo e('Transactions'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Home Page', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.home_page.hero_section.slider_version')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.hero_section.static_version')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.about_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.work_process_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.feature_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.counter_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.equipment_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.video_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.testimonial_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.product_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.call_to_action_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.blog_section')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.section_customization')): ?> active 
            <?php elseif(request()->routeIs('admin.home_page.partners')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#home_page">
                            <i class="fal fa-layer-group"></i>
                            <p><?php echo e('Home Page'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="home_page"
                            class="collapse 
              <?php if(request()->routeIs('admin.home_page.hero_section.slider_version')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.hero_section.static_version')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.about_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.work_process_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.feature_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.counter_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.equipment_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.video_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.testimonial_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.product_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.call_to_action_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.blog_section')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.section_customization')): ?> show 
              <?php elseif(request()->routeIs('admin.home_page.partners')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="submenu">
                                    <a data-toggle="collapse" href="#hero_section">
                                        <span class="sub-item"><?php echo e('Hero Section'); ?></span>
                                        <span class="caret"></span>
                                    </a>

                                    <div id="hero_section"
                                        class="collapse 
                    <?php if(request()->routeIs('admin.home_page.hero_section.slider_version')): ?> show 
                    <?php elseif(request()->routeIs('admin.home_page.hero_section.static_version')): ?> show <?php endif; ?>">
                                        <ul class="nav nav-collapse subnav">
                                            <?php if(
                                                $settings->theme_version == 1 ||
                                                    $settings->theme_version == 3 ||
                                                    $settings->theme_version == 4 ||
                                                    $settings->theme_version == 5): ?>
                                                <li
                                                    class="<?php echo e(request()->routeIs('admin.home_page.hero_section.slider_version') ? 'active' : ''); ?>">
                                                    <a
                                                        href="<?php echo e(route('admin.home_page.hero_section.slider_version', ['language' => $defaultLang->code])); ?>">
                                                        <span class="sub-item"><?php echo e('Slider Version'); ?></span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if($settings->theme_version == 2 || $settings->theme_version == 6 || $settings->theme_version == 5): ?>
                                                <li
                                                    class="<?php echo e(request()->routeIs('admin.home_page.hero_section.static_version') ? 'active' : ''); ?>">
                                                    <a
                                                        href="<?php echo e(route('admin.home_page.hero_section.static_version', ['language' => $defaultLang->code])); ?>">
                                                        <span class="sub-item"><?php echo e('Static Version'); ?></span>
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                        </ul>
                                    </div>
                                </li>
                                <?php if(
                                    $settings->theme_version == 1 ||
                                        $settings->theme_version == 2 ||
                                        $settings->theme_version == 3 ||
                                        $settings->theme_version == 5): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.about_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.about_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('About Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($settings->theme_version != 6): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.work_process_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.work_process_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e(__('Work Process Section')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if(
                                    $settings->theme_version == 1 ||
                                        $settings->theme_version == 2 ||
                                        $settings->theme_version == 3 ||
                                        $settings->theme_version == 5): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.feature_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.feature_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('Feature Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($settings->theme_version != 6): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.counter_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.counter_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('Counter Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.home_page.equipment_section') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.home_page.equipment_section', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Equipment Section'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.home_page.testimonial_section') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.home_page.testimonial_section', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Testimonial Section'); ?></span>
                                    </a>
                                </li>
                                <?php if($settings->theme_version != 1): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.product_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.product_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('Product Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($settings->theme_version == 1): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.call_to_action_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.call_to_action_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('Call To Action Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($settings->theme_version != 6): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.blog_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.blog_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e('Blog Section'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.home_page.section_customization') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.home_page.section_customization')); ?>">
                                        <span class="sub-item"><?php echo e(__('Section Show') . '/' . __('Hide')); ?></span>
                                    </a>
                                </li>
                                <?php if($settings->theme_version == 1 || $settings->theme_version == 2): ?>
                                    <li class="<?php echo e(request()->routeIs('admin.home_page.partners') ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('admin.home_page.partners')); ?>">
                                            <span class="sub-item"><?php echo e('Partners'); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($settings->theme_version == 3 || $settings->theme_version == 4 || $settings->theme_version == 5): ?>
                                    <li
                                        class="<?php echo e(request()->routeIs('admin.home_page.video_section') ? 'active' : ''); ?>">
                                        <a
                                            href="<?php echo e(route('admin.home_page.video_section', ['language' => $defaultLang->code])); ?>">
                                            <span class="sub-item"><?php echo e(__('Video Section')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Support Ticket', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.support_ticket.setting')): ?> active
            <?php elseif(request()->routeIs('admin.support_tickets')): ?> active
            <?php elseif(request()->routeIs('admin.support_tickets.message')): ?> active active <?php endif; ?>">
                        <a data-toggle="collapse" href="#support_ticket">
                            <i class="la flaticon-web-1"></i>
                            <p><?php echo e('Support Tickets'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="support_ticket"
                            class="collapse
              <?php if(request()->routeIs('admin.support_ticket.setting')): ?> show
              <?php elseif(request()->routeIs('admin.support_tickets')): ?> show
              <?php elseif(request()->routeIs('admin.support_tickets.message')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="<?php if(request()->routeIs('admin.support_ticket.setting')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.support_ticket.setting')); ?>">
                                        <span class="sub-item"><?php echo e('Setting'); ?></span>
                                    </a>
                                </li>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.support_tickets') && empty(request()->input('status')) ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.support_tickets')); ?>">
                                        <span class="sub-item"><?php echo e('All Tickets'); ?></span>
                                    </a>
                                </li>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.support_tickets') && request()->input('status') == 1 ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.support_tickets', ['status' => 1])); ?>">
                                        <span class="sub-item"><?php echo e('Pending Tickets'); ?></span>
                                    </a>
                                </li>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.support_tickets') && request()->input('status') == 2 ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.support_tickets', ['status' => 2])); ?>">
                                        <span class="sub-item"><?php echo e('Open Tickets'); ?></span>
                                    </a>
                                </li>
                                <li
                                    class="<?php echo e(request()->routeIs('admin.support_tickets') && request()->input('status') == 3 ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.support_tickets', ['status' => 3])); ?>">
                                        <span class="sub-item"><?php echo e('Closed Tickets'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Footer', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.footer.logo_and_image')): ?> active 
            <?php elseif(request()->routeIs('admin.footer.content')): ?> active 
            <?php elseif(request()->routeIs('admin.footer.quick_links')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#footer">
                            <i class="fal fa-shoe-prints"></i>
                            <p><?php echo e('Footer'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="footer"
                            class="collapse <?php if(request()->routeIs('admin.footer.logo_and_image')): ?> show 
              <?php elseif(request()->routeIs('admin.footer.content')): ?> show 
              <?php elseif(request()->routeIs('admin.footer.quick_links')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="<?php echo e(request()->routeIs('admin.footer.logo_and_image') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.footer.logo_and_image')); ?>">
                                        <span class="sub-item"><?php echo e('Logo & Image'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.footer.content') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.footer.content', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Content'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.footer.quick_links') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.footer.quick_links', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Quick Links'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Custom Pages', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.custom_pages')): ?> active 
            <?php elseif(request()->routeIs('admin.custom_pages.create_page')): ?> active 
            <?php elseif(request()->routeIs('admin.custom_pages.edit_page')): ?> active <?php endif; ?>">
                        <a href="<?php echo e(route('admin.custom_pages', ['language' => $defaultLang->code])); ?>">
                            <i class="la flaticon-file"></i>
                            <p><?php echo e('Custom Pages'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Blog Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.blog_management.categories')): ?> active 
            <?php elseif(request()->routeIs('admin.blog_management.blogs')): ?> active 
            <?php elseif(request()->routeIs('admin.blog_management.create_blog')): ?> active 
            <?php elseif(request()->routeIs('admin.blog_management.edit_blog')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#blog">
                            <i class="fal fa-blog"></i>
                            <p><?php echo e('Blog Management'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="blog"
                            class="collapse 
              <?php if(request()->routeIs('admin.blog_management.categories')): ?> show 
              <?php elseif(request()->routeIs('admin.blog_management.blogs')): ?> show 
              <?php elseif(request()->routeIs('admin.blog_management.create_blog')): ?> show 
              <?php elseif(request()->routeIs('admin.blog_management.edit_blog')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.blog_management.categories') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.blog_management.categories', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Categories'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php if(request()->routeIs('admin.blog_management.blogs')): ?> active 
                  <?php elseif(request()->routeIs('admin.blog_management.create_blog')): ?> active 
                  <?php elseif(request()->routeIs('admin.blog_management.edit_blog')): ?> active <?php endif; ?>">
                                    <a
                                        href="<?php echo e(route('admin.blog_management.blogs', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Blogs'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('FAQ Management', $rolePermissions))): ?>
                    <li class="nav-item <?php echo e(request()->routeIs('admin.faq_management') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.faq_management', ['language' => $defaultLang->code])); ?>">
                            <i class="la flaticon-round"></i>
                            <p><?php echo e('FAQ Management'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('FAQ Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php echo e(request()->routeIs('admin.basic_settings.contact_page') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.basic_settings.contact_page')); ?>">
                            <i class="fas fa-address-book"></i>
                            <p><?php echo e('Contact Page'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Advertise', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.advertise.settings')): ?> active 
            <?php elseif(request()->routeIs('admin.advertise.all_advertisement')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#advertise">
                            <i class="fab fa-buysellads"></i>
                            <p><?php echo e('Advertise'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="advertise"
                            class="collapse <?php if(request()->routeIs('admin.advertise.settings')): ?> show 
              <?php elseif(request()->routeIs('admin.advertise.all_advertisement')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li class="<?php echo e(request()->routeIs('admin.advertise.settings') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.advertise.settings')); ?>">
                                        <span class="sub-item"><?php echo e('Settings'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.advertise.all_advertisement') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.advertise.all_advertisement')); ?>">
                                        <span class="sub-item"><?php echo e('All Advertisement'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Announcement Popups', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.announcement_popups')): ?> active 
            <?php elseif(request()->routeIs('admin.announcement_popups.select_popup_type')): ?> active 
            <?php elseif(request()->routeIs('admin.announcement_popups.create_popup')): ?> active 
            <?php elseif(request()->routeIs('admin.announcement_popups.edit_popup')): ?> active <?php endif; ?>">
                        <a href="<?php echo e(route('admin.announcement_popups', ['language' => $defaultLang->code])); ?>">
                            <i class="fal fa-bullhorn"></i>
                            <p><?php echo e('Announcement Popups'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Payment Gateways', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.payment_gateways.online_gateways')): ?> active 
            <?php elseif(request()->routeIs('admin.payment_gateways.offline_gateways')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#payment_gateways">
                            <i class="la flaticon-paypal"></i>
                            <p><?php echo e('Payment Gateways'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="payment_gateways"
                            class="collapse 
              <?php if(request()->routeIs('admin.payment_gateways.online_gateways')): ?> show 
              <?php elseif(request()->routeIs('admin.payment_gateways.offline_gateways')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.payment_gateways.online_gateways') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.payment_gateways.online_gateways')); ?>">
                                        <span class="sub-item"><?php echo e('Online Gateways'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.payment_gateways.offline_gateways') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.payment_gateways.offline_gateways')); ?>">
                                        <span class="sub-item"><?php echo e('Offline Gateways'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Basic Settings', $rolePermissions))): ?>
                    <li
                        class="nav-item 
            <?php if(request()->routeIs('admin.basic_settings.mail_from_admin')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.mail_to_admin')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.mail_templates')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.edit_mail_template')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.breadcrumb')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.page_headings')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.plugins')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.seo')): ?> active
            <?php elseif(request()->routeIs('admin.pwa')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.maintenance_mode')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.general_settings')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.cookie_alert')): ?> active
            <?php elseif(request()->routeIs('admin.basic_settings.social_medias')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#basic_settings">
                            <i class="la flaticon-settings"></i>
                            <p><?php echo e('Basic Settings'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="basic_settings"
                            class="collapse 
              <?php if(request()->routeIs('admin.basic_settings.mail_from_admin')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.mail_to_admin')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.mail_templates')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.edit_mail_template')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.breadcrumb')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.page_headings')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.plugins')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.seo')): ?> show
              <?php elseif(request()->routeIs('admin.pwa')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.maintenance_mode')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.cookie_alert')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.general_settings')): ?> show
              <?php elseif(request()->routeIs('admin.basic_settings.social_medias')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.general_settings') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.basic_settings.general_settings')); ?>">
                                        <span class="sub-item"><?php echo e('General Settings'); ?></span>
                                    </a>
                                </li>

                                <li class="submenu">
                                    <a data-toggle="collapse" href="#mail-settings">
                                        <span class="sub-item"><?php echo e('Email Settings'); ?></span>
                                        <span class="caret"></span>
                                    </a>

                                    <div id="mail-settings"
                                        class="collapse 
                    <?php if(request()->routeIs('admin.basic_settings.mail_from_admin')): ?> show 
                    <?php elseif(request()->routeIs('admin.basic_settings.mail_to_admin')): ?> show
                    <?php elseif(request()->routeIs('admin.basic_settings.mail_templates')): ?> show
                    <?php elseif(request()->routeIs('admin.basic_settings.edit_mail_template')): ?> show <?php endif; ?>">
                                        <ul class="nav nav-collapse subnav">
                                            <li
                                                class="<?php echo e(request()->routeIs('admin.basic_settings.mail_from_admin') ? 'active' : ''); ?>">
                                                <a href="<?php echo e(route('admin.basic_settings.mail_from_admin')); ?>">
                                                    <span class="sub-item"><?php echo e('Mail From Admin'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php echo e(request()->routeIs('admin.basic_settings.mail_to_admin') ? 'active' : ''); ?>">
                                                <a href="<?php echo e(route('admin.basic_settings.mail_to_admin')); ?>">
                                                    <span class="sub-item"><?php echo e('Mail To Admin'); ?></span>
                                                </a>
                                            </li>

                                            <li
                                                class="<?php if(request()->routeIs('admin.basic_settings.mail_templates')): ?> active 
                        <?php elseif(request()->routeIs('admin.basic_settings.edit_mail_template')): ?> active <?php endif; ?>">
                                                <a href="<?php echo e(route('admin.basic_settings.mail_templates')); ?>">
                                                    <span class="sub-item"><?php echo e('Mail Templates'); ?></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.breadcrumb') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.basic_settings.breadcrumb')); ?>">
                                        <span class="sub-item"><?php echo e('Breadcrumb'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.pwa') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.pwa')); ?>">
                                        <span class="sub-item"><?php echo e('PWA Setting'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.page_headings') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.basic_settings.page_headings', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Page Headings'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.basic_settings.plugins') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.basic_settings.plugins')); ?>">
                                        <span class="sub-item"><?php echo e('Plugins'); ?></span>
                                    </a>
                                </li>

                                <li class="<?php echo e(request()->routeIs('admin.basic_settings.seo') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.basic_settings.seo', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('SEO Informations'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.maintenance_mode') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.basic_settings.maintenance_mode')); ?>">
                                        <span class="sub-item"><?php echo e('Maintenance Mode'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.cookie_alert') ? 'active' : ''); ?>">
                                    <a
                                        href="<?php echo e(route('admin.basic_settings.cookie_alert', ['language' => $defaultLang->code])); ?>">
                                        <span class="sub-item"><?php echo e('Cookie Alert'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.basic_settings.social_medias') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.basic_settings.social_medias')); ?>">
                                        <span class="sub-item"><?php echo e('Social Medias'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Admin Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.admin_management.role_permissions')): ?> active 
            <?php elseif(request()->routeIs('admin.admin_management.role.permissions')): ?> active 
            <?php elseif(request()->routeIs('admin.admin_management.registered_admins')): ?> active <?php endif; ?>">
                        <a data-toggle="collapse" href="#admin">
                            <i class="fal fa-users-cog"></i>
                            <p><?php echo e('Admin Management'); ?></p>
                            <span class="caret"></span>
                        </a>

                        <div id="admin"
                            class="collapse 
              <?php if(request()->routeIs('admin.admin_management.role_permissions')): ?> show 
              <?php elseif(request()->routeIs('admin.admin_management.role.permissions')): ?> show 
              <?php elseif(request()->routeIs('admin.admin_management.registered_admins')): ?> show <?php endif; ?>">
                            <ul class="nav nav-collapse">
                                <li
                                    class="<?php if(request()->routeIs('admin.admin_management.role_permissions')): ?> active 
                  <?php elseif(request()->routeIs('admin.admin_management.role.permissions')): ?> active <?php endif; ?>">
                                    <a href="<?php echo e(route('admin.admin_management.role_permissions')); ?>">
                                        <span class="sub-item"><?php echo e('Role & Permissions'); ?></span>
                                    </a>
                                </li>

                                <li
                                    class="<?php echo e(request()->routeIs('admin.admin_management.registered_admins') ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('admin.admin_management.registered_admins')); ?>">
                                        <span class="sub-item"><?php echo e('Registered Admins'); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                <?php endif; ?>

                
                <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Language Management', $rolePermissions))): ?>
                    <li
                        class="nav-item <?php if(request()->routeIs('admin.language_management')): ?> active 
            <?php elseif(request()->routeIs('admin.language_management.edit_keyword')): ?> active <?php endif; ?>">
                        <a href="<?php echo e(route('admin.language_management')); ?>">
                            <i class="fal fa-language"></i>
                            <p><?php echo e('Language Management'); ?></p>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/backend/partials/side-navbar.blade.php ENDPATH**/ ?>