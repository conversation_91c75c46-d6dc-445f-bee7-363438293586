<?php

namespace App\Models\HomePage;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogSection extends Model
{
  use HasFactory;

  /**
   * The attributes that aren't mass assignable.
   *
   * @var array
   */
  protected $fillable = ['language_id', 'title', 'subtitle', 'blog_section_image'];

  public function language()
  {
    return $this->belongsTo(Language::class, 'language_id', 'id');
  }
}
