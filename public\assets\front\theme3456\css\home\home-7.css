/* -----------------------------------------
	Hero <PERSON> 7
----------------------------------------- */
.hero-banner.hero-banner_v7 {
  padding-top: 163px;
}
.hero-banner.hero-banner_v7 .banner-filter-form .grid .item {
  width: calc(33.3333333333% - 10px);
}
.hero-banner.hero-banner_v7 .banner-filter-form .form-wrapper {
  padding-inline-start: 15px;
}
.hero-banner.hero-banner_v7 .shape > * {
  position: absolute;
  z-index: -1;
}
.hero-banner.hero-banner_v7 .shape > *.shape-1 {
  top: 15%;
  left: 6%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-2 {
  top: 45%;
  left: 48%;
  animation: moveUpDown 5s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-3 {
  bottom: 12%;
  left: 6%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-4 {
  bottom: 5%;
  left: 36%;
  animation: moveLeftRight 8s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-5 {
  top: 40%;
  right: 5%;
  animation: moveUpDown 10s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-6 {
  top: 14%;
  right: 28%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v7 .shape > *.shape-7 {
  bottom: 5%;
  right: 15%;
  animation: moveUpDown 10s linear infinite;
}