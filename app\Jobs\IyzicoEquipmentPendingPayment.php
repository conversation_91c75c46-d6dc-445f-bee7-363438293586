<?php

namespace App\Jobs;


use App\Models\Vendor;
use App\Models\Earning;
use App\Models\Commission;
use Illuminate\Bus\Queueable;
use App\Models\Instrument\Equipment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Session;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Instrument\EquipmentBooking;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Controllers\FrontEnd\Instrument\BookingProcessController;

class IyzicoEquipmentPendingPayment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $booking_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($booking_id)
    {
        $this->booking_id = $booking_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $bookingInfo = EquipmentBooking::where('id', $this->booking_id)->first();
        $conversion_id = $bookingInfo->conversation_id;

        $options = new \Iyzipay\Options();
        $options->setApiKey(config('Iyzico.api_key'));
        $options->setSecretKey(config('Iyzico.secret_key'));
        $options->setBaseUrl(config('Iyzico.base_url'));

        $request = new \Iyzipay\Request\ReportingPaymentDetailRequest();
        $request->setPaymentConversationId($conversion_id);

        $paymentResponse = \Iyzipay\Model\ReportingPaymentDetail::create($request, $options);

        $result = (array) $paymentResponse;
        foreach ($result as $key => $data) {
            if (is_string($data)) {
            $data = json_decode($data, true);
            if ((isset($data['status']) == 'success') && (count($data['payments'])> 0)) {
                if (is_array($data['payments'])) {
                    if ($data['payments'][0]['paymentStatus'] == 1) {
                        // generate an invoice in pdf format
                        $bookingProcess = new BookingProcessController();
                        $bookingInfo->update(['payment_status' => 'completed']);
                        $invoice = $bookingProcess->generateInvoice($bookingInfo);

                        $equipment = Equipment::findOrFail($bookingInfo->equipment_id);
                        if (!empty($equipment)) {
                            if ($equipment->vendor_id != null) {
                                $vendor_id = $equipment->vendor_id;
                            } else {
                                $vendor_id = null;
                            }
                        } else {
                            $vendor_id = null;
                        }
                        //calculate commission
                        $percent = Commission::select('equipment_commission')->first();

                        $commission = (($bookingInfo->total - $bookingInfo->discount) * $percent->equipment_commission) / 100;

                        //get vendor
                        $vendor = Vendor::where('id', $bookingInfo->vendor_id)->first();

                        //add blance to admin revinue
                        $earning = Earning::first();

                        $earning->total_revenue = $earning->total_revenue + $bookingInfo->grand_total;
                        if ($vendor) {
                            $earning->total_earning = $earning->total_earning + $commission + $bookingInfo->tax;
                        } else {
                            $earning->total_earning = $earning->total_earning + ($bookingInfo->grand_total - $bookingInfo->security_deposit_amount);
                        }
                        $earning->save();

                        //store Balance  to vendor
                        if ($vendor) {
                            $pre_balance = $vendor->amount;
                            $vendor->amount = $vendor->amount + ($bookingInfo->grand_total - ($commission + $bookingInfo->tax + $bookingInfo->security_deposit_amount));
                            $vendor->save();
                            $after_balance = $vendor->amount;

                            $received_amount = $bookingInfo->grand_total - ($commission + $bookingInfo->tax + $bookingInfo->security_deposit_amount);

                            // then, update the invoice field info in database
                            $bookingInfo->update([
                                'invoice' => $invoice,
                                'comission' => $commission,
                                'received_amount' => $received_amount,
                            ]);
                        } else {
                            // then, update the invoice field info in database
                            $bookingInfo->update([
                                'invoice' => $invoice,
                            ]);
                            $received_amount = $bookingInfo->grand_total - ($bookingInfo->security_deposit_amount + $bookingInfo->tax);
                            $after_balance = null;
                            $pre_balance = null;
                        }
                        //calculate commission end

                        if (!is_null($vendor_id)) {
                            $comission = $bookingInfo->comission;
                        } else {
                            $comission = $bookingInfo->grand_total - ($bookingInfo->security_deposit_amount + $bookingInfo->tax);
                        }

                        //store data to transcation table
                        $transactionStoreArr = [
                            'transcation_id' => time(),
                            'booking_id' => $bookingInfo->id,
                            'transcation_type' => 1,
                            'user_id' => Auth::guard('web')->check() == true ? Auth::guard('web')->user()->id : null,
                            'vendor_id' => $vendor_id,
                            'payment_status' => 1,
                            'payment_method' => $bookingInfo->payment_method,
                            'shipping_charge' => $bookingInfo->shipping_cost,
                            'commission' => $comission,
                            'security_deposit' => $bookingInfo->security_deposit_amount,
                            'tax' => $bookingInfo->tax,
                            'grand_total' => $received_amount,
                            'pre_balance' => $pre_balance,
                            'after_balance' => $after_balance,
                            'gateway_type' => $bookingInfo->gateway_type,
                            'currency_symbol' => $bookingInfo->currency_symbol,
                            'currency_symbol_position' => $bookingInfo->currency_symbol_position,
                        ];

                        storeTranscation($transactionStoreArr);

                        // send a mail to the customer with the invoice
                        $bookingProcess->prepareMail($bookingInfo, $transactionStoreArr['transcation_id']);

                        // remove all session data
                        Session::forget('totalPrice');
                        Session::forget('equipmentDiscount');
                        \Artisan::call('queue:work --stop-when-empty');

                        // return redirect()->route('equipment.make_booking.complete');
                    }
                }
            }
        }
        }
    }
}
