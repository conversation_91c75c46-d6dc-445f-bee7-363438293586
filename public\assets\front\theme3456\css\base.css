/* -----------------------------------------
    Variables CSS
----------------------------------------- */
:root {
    --font-heading: "Exo 2", sans-serif;
    --font-body: "Roboto", sans-serif;
    --font-icon: "Font Awesome 5 Pro";
    --font-base: 16px;
    --font-sm: 14px;
    --font-xsm: 12px;
    --font-lg: 18px;
    --font-normal: 400;
    --font-medium: 500;
    --font-semi-bold: 600;
    --font-bold: 700;
    --font-size--h1: 64px;
    --font-size--h2: 48px;
    --font-size--h3: 32px;
    --font-size--h4: 24px;
    --font-size--h5: 20px;
    --font-size--h6: 18px;
    --color-primary: #F7941E;
    --color-secondary: #F7941E;
    --color-red: #EB4C4C;
    --color-yellow: #e59819;
    --color-green: #00B67A;
    --color-blue: #0d6efd;
    --color-white: #ffffff;
    --color-black: #000000;
    --color-dark: #121212;
    --color-light: #b8b5b5;
    --color-medium: #666666;
    --color-primary-rgb: 247, 148, 30;
    --color-secondary-rgb: 247, 148, 30;
    --color-red-rgb: 235, 76, 76;
    --color-green-rgb: 0, 182, 122;
    --color-blue-rgb: 13, 110, 253;
    --color-white-rgb: 255, 255, 255;
    --color-black-rgb: 0, 0, 0;
    --color-dark-rgb: 18, 18, 18;
    --color-medium-rgb: 141, 141, 141;
    --color-light-rgb: 242, 242, 242;
    --border-color: #e8e8e8;
    --border-color-2: #a7a7a7;
    --text-dark: var(--color-dark);
    --text-medium: var(--color-medium);
    --text-white: var(--color-white);
    --text-light: var(--color-light);
    --btn-color: var(--text-white);
    --btn-hover-color: var(--text-white);
    --bg-1: #F3F3F3;
    --bg-2: #F8F8F8;
    --bg-3: #ebebeb;
    --bg-white: var(--color-white);
    --bg-dark: #0A0909;
    --bg-dark-2: #161616;
    --bg-primary-light: rgba(var(--color-primary-rgb), .06);
    --gradient-1: linear-gradient(145deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    --shadow-md: 0 5px 30px -5px rgba(8, 0, 42, .1);
    --shadow-round: 0 15px 30px -15px rgba(8, 0, 42, .5);
    --radius-sm: 6px;
    --radius-md: 10px;
    --radius-lg: 15px;
    --radius-xl: 20px;
    --radius-pill: 50rem;
    --radius-circle: 50%;
}

/* -----------------------------------------
    Utilities CSS
----------------------------------------- */
/* margin CSS*/
.mb-10 {
    margin-bottom: 10px !important;
}

.mb-15 {
    margin-bottom: 15px !important;
}

.mb-20 {
    margin-bottom: 20px !important;
}

.mb-25 {
    margin-bottom: 25px !important;
}

.mb-30 {
    margin-bottom: 30px !important;
}

.mb-40 {
    margin-bottom: 40px !important;
}

.mb-50 {
    margin-bottom: 50px !important;
}

.mb-100 {
    margin-bottom: 100px !important;
}

.mt-10 {
    margin-top: 10px !important;
}

.mt-15 {
    margin-top: 15px !important;
}

.mt-20 {
    margin-top: 20px !important;
}

.mt-25 {
    margin-top: 25px !important;
}

.mt-30 {
    margin-top: 30px !important;
}

.mt-40 {
    margin-top: 40px !important;
}

.mt-50 {
    margin-top: 50px !important;
}

.mtn-50 {
    margin-top: -50px;
}

/* Padding CSS*/
.pt-10 {
    padding-top: 10px !important;
}

.pt-15 {
    padding-top: 15px !important;
}

.pt-20 {
    padding-top: 20px !important;
}

.pt-25 {
    padding-top: 25px !important;
}

.pt-30 {
    padding-top: 30px !important;
}

.pt-40 {
    padding-top: 40px !important;
}

.pt-60 {
    padding-top: 60px !important;
}

.pt-70 {
    padding-top: 70px;
}

.pt-75 {
    padding-top: 75px;
}

.pt-80 {
    padding-top: 80px;
}

.pt-90 {
    padding-top: 90px;
}

.pt-100 {
    padding-top: 100px;
}

.pt-120 {
    padding-top: 120px;
}

.pb-10 {
    padding-bottom: 10px !important;
}

.pb-20 {
    padding-bottom: 20px !important;
}

.pb-25 {
    padding-bottom: 25px !important;
}

.pb-30 {
    padding-bottom: 30px !important;
}

.pb-40 {
    padding-bottom: 40px !important;
}

.pb-45 {
    padding-bottom: 45px !important;
}

.pb-60 {
    padding-bottom: 60px !important;
}

.pb-70 {
    padding-bottom: 70px;
}

.pb-75 {
    padding-bottom: 75px;
}

.pb-80 {
    padding-bottom: 80px;
}

.pb-90 {
    padding-bottom: 90px;
}

.pb-100 {
    padding-bottom: 100px;
}

.pb-120 {
    padding-bottom: 120px;
}

.px-60 {
    padding-inline: 60px;
}

.px-30 {
    padding-inline: 20px;
}

.px-20 {
    padding-inline: 20px;
}

.px-25 {
    padding-inline: 25px;
}

.py-20 {
    padding: 20px 0;
}

.py-25 {
    padding: 25px 0;
}

.ptb-100 {
    padding-top: 100px;
    padding-bottom: 100px;
}

.ptb-120 {
    padding-top: 120px;
    padding-bottom: 120px;
}

.ptb-90 {
    padding-top: 90px;
    padding-bottom: 90px;
}

.ptb-80 {
    padding-top: 80px;
    padding-bottom: 80px;
}

.ptb-70 {
    padding-top: 70px;
    padding-bottom: 70px;
}

.ptb-60 {
    padding-top: 60px;
    padding-bottom: 60px;
}

.ptb-50 {
    padding-top: 50px;
    padding-bottom: 50px;
}

.ptb-30 {
    padding-top: 30px;
    padding-bottom: 30px;
}

.p-10 {
    padding: 10px !important;
}

.p-15 {
    padding: 15px !important;
}

.p-20 {
    padding: 20px !important;
}

.p-25 {
    padding: 25px !important;
}

.p-30 {
    padding: 30px !important;
}

.p-40 {
    padding: 40px !important;
}

.pb-0 {
    padding-bottom: 0 !important;
}

/* Border */
.border,
.border-top,
.border-bottom,
.border-end,
.border-start {
    border-color: var(--border-color) !important;
}

/* Border Radius */
.radius-0 {
    border-radius: 0 !important;
}

.radius-sm {
    border-radius: var(--radius-sm) !important;
}

.radius-md {
    border-radius: var(--radius-md) !important;
}

.radius-lg {
    border-radius: var(--radius-lg) !important;
}

.radius-xl {
    border-radius: var(--radius-xl) !important;
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-round {
    box-shadow: var(--shadow-round);
}

.opacity-1 {
    opacity: 1 !important;
}

.opacity-65 {
    opacity: 0.65 !important;
}

.opacity-80 {
    opacity: 0.8 !important;
}

.opacity-85 {
    opacity: 0.85 !important;
}

.opacity-90 {
    opacity: 0.9 !important;
}

.mw-75 {
    max-width: 75%;
}

.mw-80 {
    max-width: 80%;
}

.z-1 {
    z-index: 1 !important;
}

.z-2 {
    z-index: 2 !important;
}

.z-3 {
    z-index: 3 !important;
}

:is(.lc-1, .lc-2, .lc-3) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.lc-1 {
    -webkit-line-clamp: 1 !important;
    line-clamp: 1 !important;
}

.lc-2 {
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
}

.lc-3 {
    -webkit-line-clamp: 3 !important;
    line-clamp: 3 !important;
}

.gap-15 {
    gap: 15px !important;
}

.gap-20 {
    gap: 20px !important;
}

.gap-25 {
    gap: 25px !important;
}

/* Tooltip CSS */
.tooltip {
    --bs-tooltip-bg: var(--color-primary);
    --bs-tooltip-padding-x: 10px;
    --bs-tooltip-padding-y: 5px;
    --bs-tooltip-font-size: var(--font-xsm);
}

.no-animation {
    transform: none;
    animation: none;
}

/* Badge CSS */
.badge {
    font-size: var(--font-xsm);
    font-weight: var(--font-medium);
}

.badge.size-md {
    padding: 8px 12px;
}

.badge.bg-success {
    color: var(--color-green);
    background: rgba(var(--color-green-rgb), 0.12) !important;
}

.badge.bg-info {
    color: var(--color-blue);
    background: rgba(var(--color-blue-rgb), 0.12) !important;
}

.badge.bg-danger {
    color: var(--color-red);
    background: rgba(var(--color-red-rgb), 0.12) !important;
}

.badge.bg-warning {
    color: var(--color-yellow);
    background: rgba(var(--color-yellow), 0.2) !important;
}

/* Card CSS */
/* Card CSS */
.card {
    --bs-card-bg: var(--bg-white);
    --bs-body-color: var(--text-medium);
    border: none;
    border-radius: 0;
    transition: all 0.3s;
}

.card .card-title {
    margin-bottom: 20px;
}

.card .card_text {
    color: var(--text-medium);
}

.card.hover-shadow:hover {
    border-color: transparent !important;
    box-shadow: var(--shadow-md);
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-black);
    opacity: 0.4;
    z-index: -1;
}

.bg-shape {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-shape.h-auto img {
    vertical-align: bottom;
}

.bg-shape img {
    vertical-align: top;
}

.progress {
    height: 10px;
    border-radius: 30px;
}

.progress .progress-bar {
    border-radius: 30px;
    background: var(--color-primary);
}

ul.list-bullet {
    list-style-type: disc !important;
}

ul.list-bullet li {
    list-style-type: disc !important;
}

@media (max-width: 991.98px) {
    .fluid-left {
        padding-inline: 0;
    }

    .fluid-right {
        padding-inline: 0;
    }
}

@media (min-width: 992px) {
    .img-left {
        margin-inline-start: -20px;
    }

    .img-right {
        margin-inline-end: -20px;
    }

    .container-lg-fluid {
        max-width: 100%;
    }

    .fluid-left {
        padding-inline-start: calc((100vw - 960px) / 2);
        padding-inline-end: 0;
    }

    .fluid-right {
        padding-inline-end: calc((100vw - 960px) / 2);
        padding-inline-start: 0;
    }

    .border-lg-end {
        border-inline-end: 1px solid var(--border-color) !important;
    }
}

@media (min-width: 1200px) {
    .img-left {
        margin-inline-start: -30px;
    }

    .img-right {
        margin-inline-end: -30px;
    }

    .fluid-left {
        padding-inline-start: calc((100vw - 1140px) / 2);
    }

    .fluid-right {
        padding-inline-end: calc((100vw - 1140px) / 2);
    }

    .border-xl-end {
        border-inline-end: 1px solid var(--border-color) !important;
    }
}

@media (min-width: 1300px) {
    .img-left {
        margin-inline-start: -50px;
    }

    .img-right {
        margin-inline-end: -50px;
    }
}

@media (min-width: 1400px) {
    .fluid-left {
        padding-inline-start: calc((100vw - 1320px) / 2);
    }

    .fluid-right {
        padding-inline-end: calc((100vw - 1320px) / 2);
    }

    .img-left {
        margin-inline-start: -40px;
    }

    .img-right {
        margin-inline-end: -40px;
    }
}

@media (min-width: 1500px) {
    .img-left {
        margin-inline-start: -80px;
    }

    .img-right {
        margin-inline-end: -80px;
    }
}

.bg-s-contain {
    background-repeat: no-repeat;
    background-size: contain !important;
}

.bg-a-fixed {
    background-attachment: fixed;
}

.bg-cover {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
}

.mfp-bg {
    background: rgba(var(--color-dark-rgb), 0.7);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    opacity: 1;
}

@media (max-width: 1199.98px) {
    .shape {
        display: none;
    }
}

.show-more {
    cursor: pointer;
}

.clients-avatar {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.clients-avatar .client-img {
    z-index: 1;
    position: relative;
    --size: 40px;
}

.clients-avatar .client-img img {
    width: var(--size);
    height: var(--size);
    border-radius: 50%;
    border: 3px solid var(--colo-white);
}

.clients-avatar .client-img img:not(:first-child) {
    margin-inline-start: -16px;
}

.clients-avatar .client-img span {
    display: inline-block;
    position: relative;
    width: var(--size);
    height: var(--size);
    text-align: center;
    line-height: var(--size);
    border-radius: 50%;
    color: var(--text-white);
    margin-inline-start: -16px;
    border: 3px solid var(--colo-white);
    z-index: 2;
    font-size: var(--font-xsm);
    background-color: var(--color-primary);
}

/* -----------------------------------------
    Typography CSS
----------------------------------------- */
body {
    font-family: var(--font-body);
    font-size: var(--font-base);
    font-weight: var(--font-normal);
    letter-spacing: 0.05em;
    color: var(--text-medium);
    overflow-x: hidden;
    background-color: var(--bg-white);
}

a {
    color: var(--text-dark);
    text-decoration: none;
    outline: 0 !important;
    transition: all 0.3s ease-out;
}

a:hover {
    color: var(--color-primary);
}

a.hover-primary:hover {
    color: var(--color-primary) !important;
}

.color-primary a {
    color: inherit;
}

:is(h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6) a {
    color: inherit;
}

:is(h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6) a:hover {
    color: var(--color-primary);
}

:focus {
    outline: 0 !important;
    box-shadow: none !important;
}

:is(.h1, .h2, .h3, .h4, .h5, .h6) {
    display: block;
}

:is(h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6) {
    font-family: var(--font-heading);
    color: var(--text-dark);
    font-weight: var(--font-bold);
    line-height: 1.2;
}

h1,
.h1 {
    font-size: var(--font-size--h1);
}

h2,
.h2 {
    font-size: var(--font-size--h2);
}

h3,
.h3 {
    font-size: var(--font-size--h3);
}

h4,
.h4 {
    font-size: var(--font-size--h4);
}

h5,
.h5 {
    font-size: var(--font-size--h5);
}

h6,
.h6 {
    font-size: var(--font-size--h6);
}

p,
.text,
.card_text {
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 15px;
}

p:last-child,
.text:last-child,
.card_text:last-child {
    margin-bottom: 0;
}

p:last-child {
    margin-bottom: 0 !important;
}

.font-lg {
    font-size: var(--font-lg) !important;
}

.font-sm {
    font-size: var(--font-sm) !important;
}

.font-xsm {
    font-size: var(--font-xsm) !important;
}

.font-medium {
    font-weight: var(--font-medium);
}

.font-bold {
    font-weight: var(--font-bold);
}

b {
    font-weight: 500;
}

.subtitle {
    color: var(--text-medium);
}

.text-end {
    text-align: end !important;
}

.text-start {
    text-align: start !important;
}

.title-shape {
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    margin-inline-start: -15px;
    margin-inline-end: auto;
    width: 25px;
}

/* -----------------------------------------
    Reset CSS
----------------------------------------- */
:focus {
    outline: none;
}

img {
    max-width: 100%;
}

.form-control:focus {
    box-shadow: none;
}

ul {
    padding: 0;
}

figure,
blockquote,
ul {
    margin-bottom: 0;
}

table,
th,
td {
    vertical-align: middle;
}

/* -----------------------------------------
	Color CSS
----------------------------------------- */
/* Theme Color CSS */
.theme-color-1 {
    --color-primary: #F7941E;
    --color-primary-rgb: 247, 148, 30;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

.theme-color-2 {
    --color-primary: #2E3192;
    --color-primary-rgb: 46, 49, 146;
    --font-heading: "Exo 2", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

.theme-color-3 {
    --color-primary: #F6A610;
    --color-primary-rgb: 246, 166, 16;
    --font-heading: "Bai Jamjuree", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

.theme-color-4 {
    --color-primary: #F63C25;
    --color-secondary: #C41330;
    --color-primary-rgb: 246, 59, 35;
    --font-heading: "Oswald", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
    --gradient-1: linear-gradient(145deg, var(--color-secondary) 0%, var(--color-primary) 100%);
}

.theme-color-5 {
    --color-primary: #FF6600;
    --color-primary-rgb: 255, 102, 0;
    --font-heading: "Syne", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

.theme-color-6 {
    --color-primary: #FEB323;
    --color-primary-rgb: 254, 178, 37;
    --font-heading: "Bai Jamjuree", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

.theme-color-7 {
    --color-primary: #FF8A8A;
    --color-primary-rgb: 255, 138, 138;
    --font-heading: "Bai Jamjuree", sans-serif;
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
}

/* Color CSS */
.color-primary {
    color: var(--color-primary) !important;
}

.color-white {
    color: var(--text-white) !important;
}

.color-light {
    color: var(--text-light) !important;
}

.color-dark {
    color: var(--color-dark) !important;
}

.color-medium {
    color: var(--text-medium) !important;
}

.color-blue {
    color: var(--color-blue) !important;
}

.color-green {
    color: var(--color-green) !important;
}

.color-red {
    color: var(--color-red) !important;
}

.color-yellow {
    color: var(--color-yellow) !important;
}

.color-purple {
    color: var(--color-primary) !important;
}

.color-orange {
    color: var(--color-orange) !important;
}

.bg-primary {
    background-color: var(--color-primary) !important;
}

.bg-primary-light {
    --bg-primary-light: rgba(var(--color-primary-rgb), .05);
    background-color: var(--bg-primary-light) !important;
}

.bg-secondary {
    background-color: var(--color-secondary) !important;
}

.bg-secondary-light {
    --bg-secondary-light: rgba(var(--color-secondary-rgb), .05);
    background-color: var(--bg-secondary-light) !important;
}

.bg-white {
    background-color: var(--bg-white) !important;
}

.bg-light {
    background-color: var(--bg-1) !important;
}

.bg-light-2 {
    background-color: var(--bg-2) !important;
}

.bg-dark {
    background-color: var(--bg-dark) !important;
}

.bg-gradient {
    background-image: var(--gradient-1) !important;
}

.border-primary {
    border-color: var(--color-primary) !important;
}

.text-gradient {
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stroke-gradient {
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke-color: var(--gradient-1);
    -webkit-text-stroke-width: 1px;
}

.bg-none {
    background-color: transparent !important;
    background: transparent !important;
}

/* -----------------------------------------
    Button CSS
----------------------------------------- */
button,
.btn {
    cursor: pointer;
    border: none;
    background: inherit;
}

button:focus,
button:active,
.btn:focus,
.btn:active {
    outline: none;
    box-shadow: none;
}

button:hover,
.btn:hover {
    outline: none;
}

.btn {
    position: relative;
    overflow: hidden;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 0;
    letter-spacing: 0.05em;
    font-weight: var(--font-medium);
    z-index: 1;
    transition: all 0.3s ease-out;
}

.icon-start i {
    margin-inline-end: 5px;
}

.icon-end i {
    margin-inline-start: 5px;
}

.btn-lg {
    padding: 14px 34px;
}

.btn-md {
    padding: 10px 24px;
    font-size: var(--font-sm);
}

.btn-sm {
    padding: 6px 14px;
    font-size: var(--font-xsm);
}

.btn-sm.btn-icon {
    width: 30px;
    height: 30px;
    font-size: 12px;
}

.btn-primary {
    color: var(--btn-color);
    background: var(--color-primary);
    border: 1px solid var(--color-primary);
    box-shadow: 0 0 40px 40px var(--color-primary) inset;
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 20px 30px -10px rgba(var(--color-primary-rgb), 0.24);
}

.btn-primary:hover {
    background-color: transparent;
    box-shadow: none;
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-primary.btn-fancy {
    -webkit-clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
    clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
    border-color: transparent;
}

.btn-primary.btn-fancy:hover {
    background-color: var(--color-white);
}

.btn-gradient {
    border-color: transparent !important;
    background-image: var(--gradient-1);
}

.btn-gradient:hover {
    color: var(--color-white) !important;
    background-image: linear-gradient(145deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.btn.no-animation {
    transform: none;
    animation: none;
    background-color: var(--color-primary);
}

.btn.no-animation::before,
.btn.no-animation::after {
    content: none;
}

.btn-danger {
    border: 1px solid var(--color-red);
    background-color: var(--color-red);
    transition: background 0.3s ease-out;
}

.btn-danger:hover {
    border-color: var(--color-red);
    background-color: var(--color-red);
}

.btn-outline {
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
    transition: all 0.3s ease-in-out;
}

.btn-outline:hover {
    background-color: transparent;
    box-shadow: 0 0 40px 40px var(--color-primary) inset;
    color: var(--btn-color);
}

.btn-secondary {
    color: var(--color-primary);
    background: var(--bg-white);
    transition: all 0.3s ease-out;
}

.btn-secondary::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: var(--bg-dark);
    transform: translateY(105%);
    transition: transform 0.4s;
    z-index: -1;
}

.btn-secondary:hover {
    color: var(--text-white);
}

.btn-secondary:hover::before {
    transform: translateY(0);
}

.btn-img {
    padding: 0;
    background-color: transparent !important;
}

.btn-img img {
    max-width: 150px;
}

.btn-img.size-sm img {
    max-width: 120px;
}

.btn-img:hover {
    border-color: var(--border-color-2) !important;
}

.btn-groups {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
}

.btn-remove {
    padding: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    padding: 0;
    background: rgba(var(--color-primary-rgb), 0.12) !important;
    color: var(--color-primary) !important;
}

.btn-text {
    cursor: pointer;
    line-height: 1;
    color: var(--text-dark);
    font-size: var(--font-sm);
    font-weight: 500;
}

.btn-text:hover {
    color: var(--color-primary) !important;
}

.btn-tag {
    display: inline-block;
    padding: 10px 24px;
    font-size: var(--font-sm);
    border-radius: 50px;
    background: var(--bg-1);
    color: var(--text-white);
    margin-bottom: 10px;
}

.btn-tag:not(:last-child) {
    margin-inline-end: 5px;
}

.btn-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 45px;
    height: 45px;
    text-align: center;
    color: var(--text-white);
    background-color: var(--color-primary);
}

.btn-icon:hover {
    color: var(--text-white);
    background-color: var(--color-primary);
}

.btn-icon-text {
    display: inline-flex;
    align-items: center;
    gap: 7px;
    --size: 30px;
}

.btn-icon-text span:last-child {
    font-weight: var(--font-medium);
}

.btn-icon-text i {
    width: var(--size);
    height: var(--size);
    line-height: var(--size);
    text-align: center;
    background-color: var(--color-primary);
    font-size: 14px;
    border-radius: 50%;
    color: var(--text-white);
    box-shadow: 0px 14px 20px 5px rgba(var(--color-primary-rgb), 0.1);
}

button.mfp-close {
    display: inline-block;
    width: 35px !important;
    height: 35px;
    line-height: 35px;
    background: var(--color-primary) !important;
    color: var(--color-white) !important;
    opacity: 1;
    right: 0 !important;
    padding: 0 !important;
    text-align: center !important;
}

.disabled {
    cursor: not-allowed !important;
    pointer-events: initial !important;
}

.btn-check:checked+.btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check)+.btn:active {
    background-color: inherit;
    border-color: inherit;
}

/* Video Btn CSS */
.video-btn {
    --size: 75px;
    --main-color: var(--color-primary);
    --main-color-rgb: var(--color-primary-rgb);
    position: relative;
    width: var(--size);
    height: var(--size);
    padding: 0;
    font-size: 22px;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    color: var(--text-white);
    background-color: var(--main-color);
}

.video-btn:hover {
    background-color: var(--main-color);
    color: var(--text-white);
}

.video-btn::before,
.video-btn::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
    bottom: 0;
    left: 0;
    border-radius: inherit;
    background-color: rgba(var(--main-color-rgb), 0.8);
}

.video-btn::after {
    animation: ripple 2s linear 1s infinite;
    z-index: -2;
}

.video-btn::before {
    animation: ripple 2s linear infinite;
    z-index: -1;
}

.video-btn-sm {
    --size: 50px;
    font-size: var(--font-sm);
}

.video-btn-sm::before,
.video-btn-sm::after {
    animation-name: ripple2;
}

.video-btn.p-absolute {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.video-btn-text {
    --size: auto;
    border: none;
    gap: 10px;
    background-color: transparent;
}

.video-btn-text::before,
.video-btn-text::after {
    content: none;
}

.video-btn-text i {
    --size: 50px;
    position: relative;
    width: var(--size);
    height: var(--size);
    padding: 0;
    font-size: 16px;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    color: var(--color-primary);
    background-color: var(--bg-white);
    border: 1px solid var(--color-primary);
}

.video-btn-text span {
    color: var(--text-medium);
}

.video-btn-text:hover {
    background-color: transparent;
    color: var(--color-primary);
}

.video-btn-text:hover span {
    color: var(--color-primary);
}

.video-btn-white {
    --main-color: var(--text-white);
    --main-color-rgb: var(--color-white-rgb);
    color: var(--color-primary);
}

.video-btn-white:hover {
    color: var(--color-primary);
}

/* -----------------------------------------
    Form CSS
----------------------------------------- */
.form-control {
    --size: 50px;
    border: 1px solid var(--border-color);
    height: var(--size);
    line-height: var(--size);
    padding: 0;
    padding-inline-start: 18px;
    padding-inline-end: 10px;
    font-size: 14px;
    color: var(--text-medium);
    background-color: transparent;
    transition: border-color 0.4s;
}

.form-control.size-md {
    --size: 40px;
    padding-inline-start: 12px;
}

.form-control:focus {
    color: var(--text-dark);
    background-color: transparent;
}

.form-control.color-white::-moz-placeholder {
    color: var(--text-white) !important;
}

.form-control.color-white::placeholder {
    color: var(--text-white) !important;
}

input:-internal-autofill-selected {
    background: unset !important;
}

:is(input:not([type=radio], [type=checkbox]), textarea, .nice-select):is(:focus, :visited, :focus-within, :focus-visible) {
    border-color: #5961F9 !important;
}

select.form-select {
    background-size: 8px !important;
}

textarea {
    height: auto;
    line-height: 1.7;
    padding: 15px 18px 18px;
}

textarea.form-control {
    min-height: 200px;
    line-height: 1.7;
    padding: 15px 18px 18px;
}

textarea::-moz-placeholder {
    font-size: var(--font-base);
}

textarea::placeholder {
    font-size: var(--font-base);
}

::-moz-placeholder {
    color: var(--text-medium) !important;
}

::placeholder {
    color: var(--text-medium) !important;
}

.custom-radio .form-radio-label {
    position: relative;
    cursor: pointer;
    padding: 0;
    padding-inline-start: 20px;
    vertical-align: middle;
}

.custom-radio .form-radio-label::before {
    position: absolute;
    content: "";
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 0.9em;
    width: 0.9em;
    border: 1px solid var(--color-primary);
    border-radius: 50%;
}

.custom-radio input[type=radio]:checked+.form-radio-label::before {
    background-color: var(--bg-white);
    border-width: 0.25em;
}

.custom-radio .input-radio {
    display: none;
}

.custom-checkbox .form-check-label {
    position: relative;
    cursor: pointer;
    position: relative;
    cursor: pointer;
    padding: 0;
    vertical-align: middle;
    padding-inline-start: 25px;
}

.custom-checkbox .form-check-label::before {
    position: absolute;
    content: "";
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 15px;
    width: 15px;
    border: 1px solid var(--color-primary);
}

.custom-checkbox input[type=checkbox]:checked+.form-check-label::after {
    opacity: 1;
}

.custom-checkbox input[type=checkbox]+.form-check-label::after {
    content: "";
    width: 9px;
    position: absolute;
    top: 50%;
    left: 2px;
    opacity: 0;
    height: 4px;
    border-left: 2px solid var(--color-white);
    border-bottom: 2px solid var(--color-white);
    transform: translateY(-100%) rotate(-45deg);
    transition: all 0.2s ease-out;
}

.custom-checkbox .input-checkbox {
    display: none;
}

input[type=checkbox]:checked+.form-check-label::before,
input[type=radio]:checked+.form-check-label::before {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

input[type=checkbox]:checked+.form-check-label::after,
input[type=radio]:checked+.form-check-label::after {
    opacity: 1;
}

.form-label {
    color: var(--text-dark);
    font-weight: var(--font-medium);
}

.form-group,
.input-group {
    position: relative;
}

.form-group.icon-end label,
.input-group.icon-end label {
    position: absolute;
    right: 0;
    left: 0;
    top: 50%;
    width: 18px;
    font-size: var(--font-sm);
    margin-inline-start: auto;
    margin-inline-end: 10px;
    transform: translateY(-50%);
}

.form-group.icon-start .form-control,
.input-group.icon-start .form-control {
    padding-inline-start: 44px;
}

.form-group.icon-start .icon,
.input-group.icon-start .icon {
    position: absolute;
    right: 0;
    left: 0;
    top: 50%;
    width: 18px;
    font-size: var(--font-sm);
    margin-inline-end: auto;
    margin-inline-start: 20px !important;
    transform: translateY(-50%);
    z-index: 1;
}

.form-group.icon-start input[type=number],
.input-group.icon-start input[type=number] {
    text-align: right;
    padding-inline-end: 10px;
}

.input-inline {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-inline .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    border-radius: 0;
    border-start-start-radius: var(--radius-sm);
    border-end-start-radius: var(--radius-sm);
}

.input-inline .btn {
    margin-inline-start: -1px;
    position: relative;
    z-index: 2;
    border-radius: 0;
    border-start-end-radius: var(--radius-sm);
    border-end-end-radius: var(--radius-sm);
}

.input-inline .btn-icon {
    width: 50px;
    height: 50px;
}

.newsletter-form .input-inline {
    flex-wrap: nowrap;
    gap: 0;
}

.newsletter-form .input-inline .form-control {
    width: 1%;
}

@media (max-width: 575.98px) {
    .newsletter-form .input-inline {
        gap: 15px;
        flex-wrap: wrap;
        border-radius: 0 !important;
        background-color: transparent !important;
    }

    .newsletter-form .input-inline .form-control {
        border-radius: var(--radius-sm) !important;
        background-color: var(--bg-white);
    }

    .newsletter-form .input-inline .btn {
        border-radius: var(--radius-sm);
    }
}

.show-password-field {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.show-password-field .show-icon {
    font-family: var(--font-icon);
    font-style: normal;
}

.show-password-field .show-icon::before {
    content: "\f06e";
}

.show-password-field .show-icon.show::before {
    content: "\f070";
}

.search-form .btn-icon {
    color: var(--text-white);
    background: var(--color-primary);
}

.search-form .input-inline {
    flex-wrap: nowrap;
}

.search-form .input-inline .form-control {
    border-radius: 0;
}

.search-form .input-inline .btn-icon {
    flex: 0 0 auto;
}

.form-check-input {
    --bs-form-control-bg: transparent;
    --bs-border-color: var(--color-primary);
}

.form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.search-box {
    position: relative;
    width: auto;
    max-width: 400px;
    margin: 0 auto;
}

.search-box .form-control {
    border-color: var(--color-primary);
}

.search-box .mfp-close {
    width: unset !important;
    height: unset !important;
    top: -50px;
    padding: 0 0 18px 10px;
    background-color: transparent !important;
}

/* -----------------------------------------
Lazyload CSS
----------------------------------------- */
.lazy-container {
    position: relative;
    overflow: hidden;
    display: table;
    table-layout: fixed;
    width: 100%;
    background-color: var(--bg-1);
    z-index: 1;
}

.lazy-container::after {
    position: absolute;
    content: "\f03e";
    font-family: "Font Awesome 5 Pro";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    place-items: center;
    font-size: 25px;
    color: var(--text-medium);
    z-index: -1;
}

.lazy-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 0px;
    height: 0px;
    max-width: 100%;
    min-width: 100%;
    max-height: 100%;
    min-height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.lazy-container img.lazy-load {
    filter: blur(5px);
    transition: filter 400ms;
}

.lazy-container img.lazyloaded {
    filter: blur(0);
}

.lazy-container.bg-none::after {
    opacity: 0;
}

.ratio::before {
    content: "";
    display: block;
    padding-bottom: 56.25%;
}

.ratio.ratio-1-1::before {
    padding-bottom: 100%;
}

.ratio.ratio-1-2::before {
    padding-bottom: 114%;
}

.ratio.ratio-1-3::before {
    padding-bottom: 126.14%;
}

.ratio.ratio-2-3::before {
    padding-bottom: 66.67%;
}

.ratio.ratio-3-4::before {
    padding-bottom: 75%;
}

.ratio.ratio-4-3::before {
    padding-bottom: 133.3333333333%;
}

.ratio.ratio-5-3::before {
    padding-bottom: 60%;
}

.ratio.ratio-5-4::before {
    padding-bottom: 80%;
}

.ratio.ratio-16-11::before {
    padding-bottom: 53.57%;
}

.ratio.ratio-16-8::before {
    padding-bottom: 50%;
}

.ratio.ratio-21-10::before {
    padding-bottom: 35%;
}

.ratio.ratio-21-9::before {
    padding-bottom: 42.85%;
}

.ratio.ratio-21-8::before {
    padding-bottom: 38.0952380952%;
}

.ratio.ratio-vertical::before {
    padding-bottom: 152%;
}

.blur-up {
    filter: blur(5px);
    transition: filter 400ms;
}

.lazyloaded.blur-up {
    filter: blur(0);
}

/* -----------------------------------------
    Nice Select CSS
----------------------------------------- */
.nice-select {
    float: unset;
    font-weight: var(--font-medium);
    padding-left: 0;
    padding-right: 0;
    padding-inline-start: 18px;
    padding-inline-end: 30px;
    background-color: inherit;
}

.nice-select:after {
    right: 0;
    left: 0;
    margin-inline-start: auto;
    margin-inline-end: 0;
}

.nice-select.form-control:after {
    margin-inline-end: 12px;
}

.nice-select .list {
    z-index: 777;
    width: 100%;
    min-width: 100px;
    font-size: var(--font-sm);
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 0;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-md);
}

.nice-select .list li {
    min-height: 30px;
    line-height: 30px;
}

.nice-select .option {
    display: block;
    font-weight: 500;
    border-inline-start: 2px solid transparent;
}

.nice-select .option:is(:hover, .focus, .selected.focus) {
    background-color: var(--bg-2);
}

.nice-select .option.selected {
    font-weight: 500;
    color: var(--color-primary) !important;
    border-color: var(--color-primary);
}

/* -----------------------------------------
  	Preloader CSS
----------------------------------------- */
#preLoader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 1000;
    background-color: var(--bg-white);
}

#preLoader .loader {
    width: 4.8px;
    height: 4.8px;
    display: block;
    margin: 20px auto;
    position: relative;
    border-radius: 4px;
    background: var(--color-primary);
    box-sizing: border-box;
    animation: animloader 0.3s 0.3s linear infinite alternate;
}

#preLoader .loader::after,
#preLoader .loader::before {
    content: "";
    box-sizing: border-box;
    width: 4.8px;
    height: 4.8px;
    border-radius: 4px;
    background: var(--color-primary);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 15px;
    animation: animloader 0.3s 0.45s linear infinite alternate;
}

#preLoader .loader::after {
    top: -15px;
    animation-delay: 0s;
}

@keyframes animloader {
    0% {
        width: 4.8px;
    }

    100% {
        width: 48px;
    }
}

/* -----------------------------------------
    Swiper Slider CSS
----------------------------------------- */
.swiper-slide {
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
}

.swiper-wrapper {
    -webkit-transform-style: preserve-3d;
}

.swiper-pagination .swiper-pagination-bullet {
    width: 24px;
    height: 6px;
    border-radius: 3px;
    opacity: 1;
    background-color: var(--color-primary);
    opacity: 0.3;
    transition: all 0.3s ease-in-out;
}

.swiper-pagination .swiper-pagination-bullet-active,
.swiper-pagination .swiper-pagination-bullet:hover {
    background-color: var(--color-primary);
    opacity: 1;
}

.swiper-pagination_circle .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    border-radius: var(--radius-circle);
}

.swiper-pagination_gradient .swiper-pagination-bullet-active,
.swiper-pagination_gradient .swiper-pagination-bullet:hover {
    background-image: var(--gradient-1);
}

.swiper-horizontal>.swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    line-height: 1;
    bottom: 0;
}

.pagination-fraction .swiper-pagination-bullet {
    position: relative;
    width: auto;
    height: auto;
    border-radius: 0;
    margin: 0;
    margin-inline: 5px;
    padding-bottom: 5px;
    text-align: center;
    font-size: var(--font-lg);
    color: var(--color-medium);
    background: transparent;
}

.pagination-fraction .swiper-pagination-bullet::before {
    position: absolute;
    content: "";
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    border-radius: 5px;
    background: var(--color-primary);
    transform: scaleX(0);
    transition: transform 0.4s ease-out;
}

.pagination-fraction .swiper-pagination-bullet-active {
    color: var(--color-primary);
}

.pagination-fraction .swiper-pagination-bullet-active::before {
    transform: none;
}

.slider-navigation {
    z-index: 3;
}

.slider-navigation .slider-btn {
    --size: 40px;
    width: var(--size);
    height: var(--size);
    text-align: center;
    background-color: var(--color-primary);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    color: var(--text-white);
    font-size: 24px;
    transition: all 0.3s ease-out;
}

.slider-navigation .slider-btn:last-child {
    margin-inline-start: 10px;
}

.slider-navigation .slider-btn i {
    transition: transform 0.3s ease-out;
}

.slider-navigation .slider-btn.btn-outline {
    background: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.slider-navigation .slider-btn.btn-outline:hover {
    color: var(--text-white);
    background: var(--color-primary);
}

.slider-navigation .slider-btn:disabled {
    cursor: not-allowed;
}

.slider-navigation.position-middle .slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 4;
    margin: 0;
}

.slider-navigation.position-middle .slider-btn.slider-btn-prev {
    left: 0%;
}

.slider-navigation.position-middle .slider-btn.slider-btn-next {
    right: 0%;
}

.slider-navigation.style-2 .slider-btn {
    background-color: var(--bg-4);
    color: var(--color-primary);
}

.swiper-lazy {
    background-color: var(--text-light);
}

.swiper-lazy-loaded.blur-up {
    filter: blur(0);
}

/* -----------------------------------------
    Nav Tabs CSS
----------------------------------------- */
.tabs-navigation::-webkit-scrollbar {
    height: 5px;
}

.tabs-navigation .nav {
    display: inline-flex;
    border: none;
    gap: 20px;
    z-index: 1;
    position: relative;
    overflow: hidden;
}

.tabs-navigation .nav li {
    flex: 0 0 auto;
}

.tabs-navigation .nav .nav-link {
    color: var(--text-dark);
    font-weight: var(--font-medium);
    margin: 0;
    z-index: 999;
    border: 0;
    cursor: pointer;
    background-color: var(--bg-primary-light);
}

.tabs-navigation .nav .nav-link:is(.active) {
    color: var(--text-white);
    background-color: var(--color-primary);
    border-radius: 0;
}

.tabs-navigation .nav[data-hover=fancyHover] .nav-link:is(.active, :hover) {
    color: var(--color-primary);
    background-color: var(--bg-primary-light);
    border-color: var(--color-primary);
}

.tabs-navigation .nav[data-hover=fancyHover] .nav-item {
    z-index: 2;
}

.tabs-navigation .nav[data-hover=fancyHover] .nav-item:is(.active) .nav-link {
    color: var(--text-white);
    background-color: var(--color-primary) !important;
    border-color: transparent;
}

.tabs-navigation .nav[data-hover=fancyHover] .nav-item:is(.active) .nav-link:is(:hover) {
    background: transparent !important;
}

.tabs-navigation .target {
    position: absolute;
    z-index: 1;
    transform: translateX(-50%);
    background-color: var(--color-primary);
    transition: all 0.2s;
}

.tabs-navigation_v2 .nav[data-hover=fancyHover] .nav-item {
    z-index: 2;
}

.tabs-navigation_v2 .nav[data-hover=fancyHover] .nav-item:is(.active) .nav-link {
    background-image: var(--gradient-1) !important;
}

.tabs-navigation_v2 .target {
    background: var(--gradient-1);
}

.tabs-navigation_v3 .nav {
    padding: 12px 15px;
    gap: 0;
    background-color: var(--bg-2);
}

.tabs-navigation_v3 .nav .nav-link {
    background-color: transparent !important;
}

.tab-pane.fade {
    transition: all 0.3s;
    transform: translateY(0.5rem);
}

.tab-pane.fade.show {
    transform: translateY(0rem);
}

.tab-pane.slide.show {
    animation: slideUp 1 0.4s;
}

/* -----------------------------------------
  	Go top CSS
----------------------------------------- */
.go-top {
    --size: 40px;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
    left: 0;
    width: var(--size);
    height: var(--size);
    opacity: 0;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-white);
    font-size: 20px;
    background-color: var(--color-primary);
    transition: 0.4s;
    transform: translateX(15%);
    z-index: 100;
}

.go-top.active {
    opacity: 1;
    transform: none;
}

.go-top:hover {
    transform: translateY(-3px);
}

.go-top_gradient {
    background-image: var(--gradient-1);
}

/* -----------------------------------------
    Title CSS
----------------------------------------- */
.section-title .title {
    margin-bottom: 0;
    position: relative;
    margin-top: -8px;
}

@media (max-width: 767.98px) {
    .section-title .title {
        margin-top: -5px;
    }
}

.section-title .title span {
    position: relative;
}

.section-title .subtitle {
    display: block;
    margin-bottom: 10px;
}

.section-title .subtitle_v2 {
    padding: 7px 16px;
    width: -moz-fit-content;
    width: fit-content;
    background-color: var(--color-primary);
    background-image: var(--gradient-1);
    color: var(--text-white);
    -webkit-clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
    clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
}

.section-title .subtitle_v3 {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.section-title .subtitle_v3 .line {
    position: relative;
    overflow: hidden;
    display: inline-block;
    border-inline-start: 6px solid var(--color-primary);
    width: 26px;
    height: 18px;
    transform: skewX(-10deg);
}

.section-title .subtitle_v3 .line::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    bottom: 0;
    width: 6px;
    height: 100%;
    margin-inline-start: 4px;
    background-color: var(--color-primary);
}

.section-title .subtitle_v3 .line::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 6px;
    height: 100%;
    margin-inline-start: auto;
    margin-inline-end: 0;
    background-color: var(--color-primary);
}

.section-title.title-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

@media (min-width: 768px) {
    .section-title.title-inline>div {
        flex: 0 0 auto;
        max-width: calc(50% - 10px);
    }
}

.section-title.title-center {
    text-align: center;
}

.section-title.title-center .title {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-title.title-center .title .title-shape {
    margin-inline-start: 0;
}

.section-title.title-center .subtitle_v2 {
    margin-inline: auto;
}

.section-title.title-center p {
    max-width: 500px;
    margin-inline: auto;
    margin-bottom: 0;
}

.content-title .title {
    position: relative;
    margin-bottom: 10px;
    margin-top: -8px;
}

@media (max-width: 767.98px) {
    .content-title .title {
        margin-top: -5px;
    }
}

.content-title .subtitle {
    display: block;
    margin-bottom: 10px;
}

.content-title .subtitle_v2 {
    padding: 7px 16px;
    width: -moz-fit-content;
    width: fit-content;
    background-color: var(--color-primary);
    background-image: var(--gradient-1);
    color: var(--text-white);
    -webkit-clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
    clip-path: polygon(5% 0, 100% 0%, 95% 100%, 0% 100%);
}

.content-title .subtitle_v3 {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.content-title .subtitle_v3 .line {
    position: relative;
    overflow: hidden;
    display: inline-block;
    border-inline-start: 6px solid var(--color-primary);
    width: 26px;
    height: 18px;
    transform: skewX(-10deg);
}

.content-title .subtitle_v3 .line::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    bottom: 0;
    width: 6px;
    height: 100%;
    margin-inline-start: 4px;
    background-color: var(--color-primary);
}

.content-title .subtitle_v3 .line::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 6px;
    height: 100%;
    margin-inline-start: auto;
    margin-inline-end: 0;
    background-color: var(--color-primary);
}

.content-title p:last-child {
    margin-bottom: 0;
}

/* -----------------------------------------
  	Animation CSS
----------------------------------------- */
@keyframes pulse {
    from {
        transform: scale3d(1, 1, 1);
    }

    50% {
        transform: scale3d(1.2, 1.2, 1.2);
    }

    to {
        transform: scale3d(1, 1, 1);
    }
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(10%);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-50%);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes moveAround {
    0% {
        transform: translate(0px, 0px) rotate(0deg);
    }

    20% {
        transform: translate(73px, -1px) rotate(36deg);
    }

    40% {
        transform: translate(141px, 72px) rotate(72deg);
    }

    60% {
        transform: translate(83px, 122px) rotate(108deg);
    }

    80% {
        transform: translate(-40px, 72px) rotate(144deg);
    }

    100% {
        transform: translate(0px, 0px) rotate(0deg);
    }
}

@keyframes moveUpDown {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-20px);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes moveLeftRight {
    0% {
        transform: translateX(0);
    }

    50% {
        transform: translateX(-20px);
    }

    100% {
        transform: translateX(0);
    }
}

@keyframes ripple {
    0% {
        opacity: 0.45;
    }

    100% {
        opacity: 0;
        transform: scale(2);
    }
}

/* -----------------------------------------
    Social Link CSS
----------------------------------------- */
.social-link {
    --size: 36px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.social-link a {
    text-align: center;
    width: var(--size);
    height: var(--size);
    line-height: var(--size);
    background-color: var(--color-primary);
    color: var(--text-white);
    font-size: var(--font-base);
}

.social-link a:last-child {
    margin: 0;
}

.social-link a:hover {
    color: var(--text-white);
    background-color: var(--color-primary);
}

.social-link.style-2 a {
    background-color: rgba(var(--color-primary-rgb), 0.1);
    color: var(--color-primary);
}

.social-link.style-2.social-link_gradient a {
    background-image: none;
}

.social-link.style-2.social-link_gradient a i {
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.social-link.icon-only {
    --size: auto;
}

.social-link.icon-only a {
    background-color: transparent;
}

.social-link.size-md {
    --size: 30px;
    --font-base: 14px;
}

.social-link.size-lg {
    --size: 50px;
}

.social-link.rounded a {
    border-radius: 50%;
}

.social-link_gradient a {
    background-image: var(--gradient-1);
}

/* -----------------------------------------
    Ratings CSS
----------------------------------------- */
.ratings {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-inline-start: -3px;
    gap: 5px;
}

.ratings .rate {
    background-position: 0 -13px;
    background-repeat: repeat-x !important;
    height: 13px;
    transition: all 0.5s ease-out 0s;
    width: 90px;
}

.ratings .rating-icon {
    background-position: 0 0;
    background-repeat: repeat-x !important;
    height: 13px;
    width: 80%;
}

.ratings .ratings-total {
    font-size: 12px;
    line-height: 1;
}

.ratings.size-md .rate {
    background-position: 0 -15px;
    height: 15px;
    width: 115px;
}

.ratings.size-md .rating-icon {
    height: 15px;
}

.ratings.size-md .ratings-total {
    font-size: 14px;
}

.ratings.size-lg .rate {
    background-position: 0 -20px;
    height: 20px;
    width: 145px;
}

.ratings.size-lg .rating-icon {
    height: 20px;
}

.ratings.size-lg .ratings-total {
    font-size: 16px;
}

/* -----------------------------------------
	Datepicker CSS
----------------------------------------- */
.daterangepicker {
    padding: 10px;
    background-color: var(--bg-white);
    border-width: 0;
    border-radius: 10px;
    box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.2);
}

.daterangepicker .available.next,
.daterangepicker .available.prev {
    border-radius: 50%;
    background: var(--bg-3);
}

.daterangepicker .available.next span,
.daterangepicker .available.prev span {
    border-color: var(--color-primary);
}

.daterangepicker .btn-primary {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.daterangepicker .calendar-table {
    background-color: transparent;
}

.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
    cursor: pointer;
    border-radius: 50%;
    min-width: 32px;
    min-height: 31px;
    line-height: 31px;
}

.daterangepicker td.start-date.end-date {
    border-radius: 50%;
    border-color: transparent;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    background: var(--color-primary);
    border-color: transparent;
}

.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.secondselect,
.daterangepicker select.ampmselect {
    height: auto;
    color: var(--base-color);
}

.daterangepicker .drp-buttons {
    border-color: var(--border-color);
}

.daterangepicker .drp-buttons .btn {
    color: var(--text-dark);
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    background: var(--color-primary) !important;
    border-color: transparent;
}

.daterangepicker td.available:hover,
.daterangepicker th.available:hover,
.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
    background-color: var(--bg-2);
}

/*---=========================
  Start Cookie Alert
=========================---*/
.cookie-consent {
    position: fixed;
    background-color: #262938;
    bottom: 0px;
    width: 100%;
    padding: 15px 0px;
    z-index: 10000000000;
    color: #fff;
}

.cookie-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

p.cookie-consent__message {
    background-color: transparent !important;
    color: #fff !important;
    font-size: 14px;
}

button.cookie-consent__agree {
    padding: 5px 16px;
    border: none;
    background-color: #25D06F;
    color: #fff;
    cursor: pointer;
    border-radius: 3px;
    font-size: 14px;
}

p.cookie-consent__message {
    background-color: transparent !important;
    font-size: 14px;
}

p.cookie-consent__message * {
    color: #fff;
}

@media only screen and (max-width: 991px) {
    .cookie-container {
        display: block;
        text-align: center;
    }

    button.cookie-consent__agree {
        margin-top: 15px;
    }

    p.cookie-consent__message {
        line-height: 20px;
        display: block;
    }
}

/*---=========================
  End Cookie Alert
=========================---*/

/*---=========================
  Start Popup
=========================---*/
.popup-wrapper {
    display: none;
}

.popup_main-content h1 {
    font-size: 40px;
    line-height: 50px;
}

.mfp-close-btn-in .mfp-close {
    color: #333;
    background: #FFF;
    opacity: 1;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    line-height: 32px;
    right: -15px;
    top: -15px;
}

.popup-wrapper {
    position: relative;
    width: auto;
    max-width: 960px;
    margin: 0 auto;
}

.popup-wrapper .form_control {
    width: 100%;
    height: 70px;
    padding: 0 30px;
    line-height: 70px;
}

/* popup one */
.popup-one {
    position: relative;
    padding: 138px 120px;
}

.popup-one .popup_main-content {
    position: relative;
    text-align: center;
    border-radius: 10px;
    padding: 90px 55px;
}

.popup-one .popup_main-content h1,
.popup-one .popup_main-content p {
    color: #fff;
}

.popup-one .popup_main-content h1 {
    margin-bottom: 20px;
}

.popup-one .popup_main-content p {
    margin-bottom: 30px;
}

.popup-one .popup_main-content .popup-main-btn {
    padding: 15px 100px;
    display: inline-block;
    color: #fff;
    border-radius: 40px;
    border: 1px solid #fff;
    text-decoration: none;
    transition: .3s;
    -webkit-transition: .3s;
    -moz-transition: .3s;
    -ms-transition: .3s;
    -o-transition: .3s;
}

.popup-one .popup_main-content .popup-main-btn:hover,
.popup-one .popup_main-content .popup-main-btn:focus {
    background-color: #451D53;
    color: #fff;
}

/* popup two */
.popup-two {
    position: relative;
    padding: 120px;
}

.popup-two .popup_main-content {
    position: relative;
    text-align: center;
    border-radius: 10px;
    padding: 90px 55px;
}

.popup-two .popup_main-content h1,
.popup-two .popup_main-content p {
    color: #fff;
}

.popup-two .popup_main-content h1 {
    margin-bottom: 20px;
}

.popup-two .popup_main-content p {
    margin-bottom: 30px;
}

.popup-two .popup_main-content .subscribe-form {
    padding: 0 70px;
}

.popup-two .popup_main-content .subscribe-form .form_control {
    border: none;
    border-radius: 10px;
    font-size: 20px;
    margin-bottom: 20px;
}

.popup-two .popup_main-content .subscribe-form .popup-main-btn {
    width: 100%;
    border: 1px solid #fff;
    background-color: transparent;
    color: #fff;
    font-size: 20px;
    height: 70px;
    border-radius: 10px;
    cursor: pointer;
    transition: all .3s;
}

.popup-two .popup_main-content .subscribe-form .popup-main-btn:hover,
.popup-two .popup_main-content .subscribe-form .popup-main-btn:focus {
    background-color: #FF2865;
    color: #fff;
}

/* popup three */
.popup-three .popup_main-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background-color: #fff;
}

.popup-three .popup_main-content .left-bg {
    width: 45%;
    height: 100%;
    min-height: 670px;
}

.popup-three .popup_main-content .right-content {
    width: 55%;
    padding: 30px 15px;
    text-align: center;
}

.popup-three .popup_main-content .right-content h1 {
    font-weight: 400;
    margin-bottom: 20px;
}

.popup-three .popup_main-content .right-content p {
    margin-bottom: 20px;
}

.popup-three .popup_main-content .right-content .popup-main-btn {
    padding: 15px 70px;
    color: #fff;
    font-size: 19px;
    border-radius: 35px;
    display: inline-block;
    text-decoration: none;
}

/* popup four */
.popup-four .popup_main-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background-color: #fff;
}

.popup-four .popup_main-content .left-bg {
    width: 45%;
    height: 100%;
    min-height: 670px;
}

.popup-four .popup_main-content .right-content {
    width: 55%;
    padding: 30px 30px;
    text-align: center;
}

.popup-four .popup_main-content .right-content h1 {
    font-weight: 400;
    margin-bottom: 20px;
}

.popup-four .popup_main-content .right-content p {
    margin-bottom: 20px;
}

.popup-four .popup_main-content .subscribe-form {
    padding: 0 70px;
}

.popup-four .popup_main-content .subscribe-form .form_control {
    border: 2px solid #C5C5C5;
    border-radius: 10px;
    font-size: 20px;
    margin-bottom: 20px;
}

.popup-four .popup_main-content .subscribe-form .popup-main-btn {
    width: 100%;
    color: #fff;
    font-size: 20px;
    height: 70px;
    border-radius: 10px;
    cursor: pointer;
    border: none;
    transition: all .3s;
}

.popup-four .popup_main-content .subscribe-form .popup-main-btn:hover,
.popup-four .popup_main-content .subscribe-form .popup-main-btn:focus {
    background-color: #F8960D;
    color: #fff;
}

/* popup five */
.popup-five .popup_main-content {
    text-align: center;
    padding: 75px;
}

.popup-five .popup_main-content h1 {
    font-size: 42px;
    line-height: 52px;
    font-weight: 400;
    color: #fff;
    margin-bottom: 20px;
    margin: 0 auto 20px;
    max-width: 600px;
}

.popup-five .popup_main-content h4 {
    color: #fff;
    font-size: 25px;
    font-weight: 35px;
    margin-bottom: 40px;
    font-weight: 400;
}

.popup-five .popup_main-content .popup-main-btn {
    padding: 15px 30px;
    display: inline-block;
    color: #fff;
    border-radius: 40px;
    text-decoration: none;
}

/* popup six */
.popup-six .popup_main-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    background-color: #fff;
    text-decoration: none;
}

.popup-six .popup_main-content .left-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
}

.popup-six .popup_main-content .right-content {
    width: 50%;
    height: 100%;
    padding: 120px 15px;
    text-align: center;
    margin-left: 50%;
}

.popup-six .popup_main-content .right-content h1 {
    font-size: 40px;
    font-weight: 400;
    line-height: 50px;
    color: #fff;
    margin-bottom: 25px;
}

.popup-six .popup_main-content h4 {
    color: #fff;
    font-size: 20px;
    line-height: 32px;
    margin-bottom: 40px;
    font-weight: 400;
}

.popup-six .popup_main-content .right-content .popup-main-btn {
    padding: 15px 30px;
    display: inline-block;
    color: #fff;
    border-radius: 40px;
    text-decoration: none;
}

/* syotimer css */
.popup-wrapper .syotimer__body {
    max-width: 420px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.popup-wrapper .syotimer__body p {
    color: #fff;
}

.popup-wrapper .syotimer-cell {
    flex: 0 0 24%;
    padding-left: 10px;
    padding-right: 10px;
}

.popup-wrapper .syotimer-cell__value {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1F3A52;
    border-radius: 10px;
    font-size: 36px;
    color: #fff;
    margin-bottom: 15px;
}

.popup-wrapper .syotimer-cell__unit {
    font-size: 20px;
    color: #fff;
    text-transform: capitalize;
}

/* medium device */
@media only screen and (min-width: 768px) and (max-width : 991px) {

    .popup-one .popup_main-content h1,
    .popup-three .popup_main-content .right-content h1,
    .popup-four .popup_main-content .right-content h1,
    .popup-five .popup_main-content h1,
    .popup-six .popup_main-content .right-content h1 {
        font-size: 40px;
        line-height: 50px;
    }

    .popup-wrapper {
        max-width: 720px;
    }

    .popup-two .popup_main-content .subscribe-form,
    .popup-four .popup_main-content .subscribe-form {
        padding: 0 15px;
    }

    .popup-one,
    .popup-two {
        padding: 80px 30px;
    }

    .syotimer__body {
        margin-bottom: 0px;
    }

    .syotimer-cell {
        flex: 0 0 50%;
        margin-bottom: 20px;
    }
}

/* small devices, tablets */
@media only screen and (max-width : 991px) {
    .popup-wrapper .syotimer-cell {
        padding-left: 5px;
        padding-right: 5px;
    }
}

@media only screen and (max-width : 767px) {
    .popup-wrapper {
        max-width: 500px;
    }

    .popup-five .popup_main-content {
        text-align: center;
        padding: 50px 15px;
    }

    .popup-three .popup_main-content .left-bg,
    .popup-four .popup_main-content .left-bg,
    .popup-six .popup_main-content .left-bg {
        display: none;
    }

    .popup-one,
    .popup-two {
        padding: 30px 15px;
    }

    .popup-one .popup_main-content,
    .popup-two .popup_main-content {
        padding: 20px 15px;
    }

    .popup-one .popup_main-content .main-btn {
        padding: 15px 30px;
    }

    .popup-three .popup_main-content .right-content,
    .popup-four .popup_main-content .right-content,
    .popup-six .popup_main-content .right-content {
        width: 100%;
        padding: 50px 15px;
    }

    .popup-two .popup_main-content .subscribe-form,
    .popup-four .popup_main-content .subscribe-form {
        padding: 0 15px;
    }

    .popup-two .popup_main-content .subscribe-form .form_control,
    .popup-four .popup_main-content .subscribe-form .form_control {
        font-size: 15px;
    }

    .popup-two .popup_main-content h1,
    .popup-one .popup_main-content h1,
    .popup-three .popup_main-content .right-content h1,
    .popup-four .popup_main-content .right-content h1,
    .popup-five .popup_main-content h1,
    .popup-six .popup_main-content .right-content h1 {
        font-size: 24px;
        line-height: 34px;
    }

    .popup-six .popup_main-content .right-content {
        margin-left: 0%;
    }

    .syotimer__body {
        max-width: 380px;
        margin-bottom: 0px;
    }

    .syotimer-cell {
        flex: 0 0 50%;
        margin-bottom: 20px;
    }
}

/* mobile devices, tablets */
@media only screen and (max-width: 400px) {
    .popup-wrapper {
        max-width: 300px;
    }

    .syotimer__body {
        max-width: 300px;
        margin-bottom: 0px;
    }

    .syotimer-cell {
        flex: 0 0 50%;
        margin-bottom: 20px;
    }
}

/* wide mobile devices */
@media only screen and (min-width: 401px) and (max-width : 767px) {
    .popup-wrapper {
        max-width: 380px;
    }
}

/*---=========================
  End Popup
=========================---*/
