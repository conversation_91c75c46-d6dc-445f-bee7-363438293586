<?php

namespace App\Http\Controllers\FrontEnd;

use App\Http\Controllers\Controller;
use App\Http\Controllers\FrontEnd\MiscellaneousController;
use App\Models\CustomPage\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{
  public function page($slug)
  {
    // Validate slug parameter
    if (empty($slug)) {
      abort(404);
    }

    $misc = new MiscellaneousController();

    $language = $misc->getLanguage();

    $queryResult['bgImg'] = $misc->getBreadcrumb();

    try {
      $queryResult['pageInfo'] = Page::join('page_contents', 'pages.id', '=', 'page_contents.page_id')
        ->where('pages.status', '=', 1)
        ->where('page_contents.language_id', '=', $language->id)
        ->where('page_contents.slug', '=', $slug)
        ->firstOrFail();
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      abort(404);
    }

    return view('frontend.custom-page', $queryResult);
  }
}
