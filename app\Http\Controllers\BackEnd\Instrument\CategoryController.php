<?php

namespace App\Http\Controllers\BackEnd\Instrument;

use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Http\Helpers\UploadFile;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\Instrument\EquipmentCategory;
use App\Models\Instrument\EquipmentCategorySection;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        // first, get the language info from db
        $language = Language::where('code', $request->language)->first();
        $information['language'] = $language;

        // then, get the equipment categories of that language from db
        $information['categories'] = $language->equipmentCategory()->orderByDesc('id')->get();
        $information['category_section'] = $language->equipmentCategorySection()->first();

        // also, get all the languages from db
        $information['langs'] = Language::all();

        return view('backend.instrument.category.index', $information);
    }

    public function updateSection(Request $request)
    {
        $language = Language::query()
            ->where('code', '=', $request->language)
            ->first();

        EquipmentCategorySection::query()->updateOrCreate(
            ['language_id' => $language->id],
            [
                'subtitle' => $request->subtitle,
                'title' => $request->title,
            ],
        );

        Session::flash('success', 'Work process section updated successfully!');

        return redirect()->back();
    }

    public function store(Request $request)
    {
        $themeInfo = DB::table('basic_settings')->select('theme_version')->first();

        $rules = [
            'language_id' => 'required',
            'name' => 'required|unique:equipment_categories|max:255',
            'status' => 'required|numeric',
            'serial_number' => 'required|numeric',
        ];

        $message = [
            'language_id.required' => 'The language field is required.',
        ];

        if ($themeInfo->theme_version == 6 && $request->hasFile('image')) {
            // store image in storage
            $imgName = UploadFile::store(public_path('assets/img/equipments/category-images/'), $request->file('image'));
        }

        $validator = Validator::make($request->all(), $rules, $message);

        if ($validator->fails()) {
            return Response::json(
                [
                    'errors' => $validator->getMessageBag(),
                ],
                400,
            );
        }

        EquipmentCategory::create(
            $request->except('slug', 'image') + [
                'slug' => createSlug($request->name),
                'image' => $request->hasFile('image') ? $imgName : null,
            ],
        );

        Session::flash('success', 'New equipment category added successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function update(Request $request)
    {
        $themeInfo = DB::table('basic_settings')->select('theme_version')->first();

        $rules = [
            'name' => ['required', 'max:255', Rule::unique('equipment_categories', 'name')->ignore($request->id, 'id')],
            'status' => 'required|numeric',
            'serial_number' => 'required|numeric',
        ];
        $category = EquipmentCategory::find($request->id);

        if ($themeInfo->theme_version == 6 && $request->hasFile('image')) {
            $newImage = $request->file('image');
            $oldImage = $category->image;
            $imgName = UploadFile::update(public_path('assets/img/equipments/category-images/'), $newImage, $oldImage);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return Response::json(
                [
                    'errors' => $validator->getMessageBag(),
                ],
                400,
            );
        }

        $category->update(
            $request->except('slug', 'image') + [
                'slug' => createSlug($request->name),
                'image' => $request->hasFile('image') ? $imgName : $category->image,
            ],
        );

        Session::flash('success', 'Equipment category updated successfully!');

        return Response::json(['status' => 'success'], 200);
    }

    public function destroy($id)
    {
        $category = EquipmentCategory::find($id);
        $equipmentContents = $category->equipmentContent()->get();

        if (count($equipmentContents) > 0) {
            return redirect()->back()->with('warning', 'First delete all the equipments of this category!');
        } else {
            @unlink(public_path('assets/img/equipments/category-images/') . $category->image);
            $category->delete();

            return redirect()->back()->with('success', 'Category deleted successfully!');
        }
    }

    public function bulkDestroy(Request $request)
    {
        $ids = $request->ids;

        $errorOccured = false;

        foreach ($ids as $id) {
            $category = EquipmentCategory::find($id);
            $equipmentContents = $category->equipmentContent()->get();

            if (count($equipmentContents) > 0) {
                $errorOccured = true;
                break;
            } else {
                @unlink(public_path('assets/img/equipments/category-images/') . $category->image);
                $category->delete();
            }
        }

        if ($errorOccured == true) {
            Session::flash('warning', 'First delete all the equipment of these categories!');
        } else {
            Session::flash('success', 'Equipment categories deleted successfully!');
        }

        return Response::json(['status' => 'success'], 200);
    }

    public function updateFeatured(Request $request, $id)
    {
        $category = EquipmentCategory::find($id);
   
        if ($request['is_featured'] == 'yes') {
            $category->update(['is_featured' => 'yes']);

            Session::flash('success', 'Equipment category featured successfully!');
        } else {
            $category->update(['is_featured' => 'no']);

            Session::flash('success', 'Equipment category unfeatured successfully!');
        }

        return redirect()->back();
    }
}
