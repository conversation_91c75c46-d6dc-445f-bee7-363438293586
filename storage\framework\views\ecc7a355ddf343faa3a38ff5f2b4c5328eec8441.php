<?php $__env->startSection('pageHeading'); ?>
  <?php if(!empty($pageHeading)): ?>
    <?php echo e($pageHeading->vendor_page_title); ?>

  <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('metaKeywords'); ?>
  <?php if(!empty($seoInfo)): ?>
    <?php echo e($seoInfo->meta_keywords_vendor_page); ?>

  <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('metaDescription'); ?>
  <?php if(!empty($seoInfo)): ?>
    <?php echo e($seoInfo->meta_description_vendor_page); ?>

  <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
  <?php if ($__env->exists('frontend.partials.breadcrumb', [
      'breadcrumb' => $bgImg->breadcrumb,
      'title' => $pageHeading ? $pageHeading->vendor_page_title : '',
  ])) echo $__env->make('frontend.partials.breadcrumb', [
      'breadcrumb' => $bgImg->breadcrumb,
      'title' => $pageHeading ? $pageHeading->vendor_page_title : '',
  ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


  <!-- Author-single-area start -->
  <div class="author-area pt-130 pb-100">
    <div class="container">
      <div class="authors-search-filter mb-60">
        <form action="<?php echo e(route('frontend.vendors')); ?>" method="GET">
          <div class="search-filter-form">
            <div class="row">
              <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="form_group">
                  <input type="text" class="form_control" placeholder="<?php echo e(__('username / shop name')); ?>" name="us_name"
                    value="<?php echo e(request()->input('us_name')); ?>">
                </div>
              </div>
              <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="form_group">
                  <input type="text" class="form_control" value="<?php echo e(request()->input('location')); ?>"
                    placeholder="<?php echo e(__('Enter Shop Location')); ?>" name="location">
                </div>
              </div>
              <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="form_group">
                  <select class="wide nice-select1" name="rating">
                    <option value=""><?php echo e(__('Show All Rating')); ?></option>
                    <option <?php echo e(request()->input('rating') == 5 ? 'selected' : ''); ?> value="5">
                      <?php echo e(__('5 Star Rating')); ?></option>
                    <option <?php echo e(request()->input('rating') == 4 ? 'selected' : ''); ?> value="4">
                      <?php echo e(__('4 Star And Higher')); ?></option>
                    <option <?php echo e(request()->input('rating') == 3 ? 'selected' : ''); ?> value="3">
                      <?php echo e(__('3 Star And Higher')); ?></option>
                    <option <?php echo e(request()->input('rating') == 2 ? 'selected' : ''); ?> value="2">
                      <?php echo e(__('2 Star And Higher')); ?></option>
                    <option <?php echo e(request()->input('rating') == 1 ? 'selected' : ''); ?> value="1">
                      <?php echo e(__('1 Star And Higher')); ?></option>
                  </select>
                </div>
              </div>
              <div class="col-lg-3 col-md-6 col-sm-12">
                <div class="form_group">
                  <button class="search-btn"><?php echo e(__('Search')); ?></button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="row">
        <div class="col-lg-12">
          <?php if(count($vendors) > 0): ?>
            <div class="row">
              <?php
                $vendorIds = [];
              ?>
              <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(!in_array($item->id, $vendorIds)): ?>
                  <div class="col-xl-3 col-lg-4 col-md-6">
                    <div class="card card-center border p-3 mb-30">
                      <figure class="card-img mx-auto mb-20">
                        <a href="<?php echo e(route('frontend.vendor.details', $item->username)); ?>" target="_self"
                          title="<?php echo e($item->username); ?>">
                          <?php if($item->photo != null): ?>
                            <img class="rounded-lg" src="<?php echo e(asset('assets/admin/img/vendor-photo/' . $item->photo)); ?>"
                              alt="Author">
                          <?php else: ?>
                            <img class="rounded-lg" src="<?php echo e(asset('assets/img/user.png')); ?>" alt="Author">
                          <?php endif; ?>

                        </a>
                      </figure>
                      <div class="card-content">
                        <h6 class="card-title mb-1"><a href="<?php echo e(route('frontend.vendor.details', $item->username)); ?>"
                            target="_self" title="<?php echo e($item->shop_name); ?>"><?php echo e($item->shop_name); ?></a>
                        </h6>


                        <h6 class="card-title mb-1 vendor_username_list"><a
                            href="<?php echo e(route('frontend.vendor.details', $item->username)); ?>" target="_self"
                            title="<?php echo e($item->username); ?>"><?php echo e($item->username); ?></a></h6>

                        <div class="ratings mt-1">
                          <div class="rate">
                            <?php
                              $avg_rating = App\Models\Instrument\EquipmentReview::where('vendor_id', $item->id)->avg('rating');
                              $review_count = App\Models\Instrument\EquipmentReview::where('vendor_id', $item->id)->count();
                            ?>
                            <div class="rating-icon" style="width: <?php echo e($avg_rating * 20); ?>%"></div>
                          </div>
                          <span class="ratings-total">( <?php echo e($review_count); ?>

                            <?php if($review_count <= 1): ?>
                              <?php echo e(__('Rating')); ?>

                            <?php else: ?>
                              <?php echo e(__('Ratings')); ?>

                            <?php endif; ?>
                            )
                          </span>
                        </div>

                        <div class="mb-15 font-sm">
                          <?php
                            $equipment_count = App\Models\Instrument\Equipment::where('vendor_id', $item->id)->count();
                          ?>

                          <span><?php echo e($equipment_count); ?>

                            <?php if($equipment_count <= 1): ?>
                              <?php echo e(__('Item')); ?>

                            <?php else: ?>
                              <?php echo e(__('Items')); ?>

                            <?php endif; ?>
                          </span>
                        </div>
                        <a href="<?php echo e(route('frontend.vendor.details', $item->username)); ?>" target="_self"
                          title="<?php echo e($item->username); ?>" class="btn-text">
                          <?php echo e(__('View Profile')); ?> </a>
                      </div>
                    </div>
                  </div>
                  <?php
                    array_push($vendorIds, $item->id);
                  ?>
                <?php endif; ?>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
            <?php echo e($vendors->links()); ?>

          <?php else: ?>
            <h2 class="text-center mb-30"><?php echo e(__('No Vendor Found') . '!'); ?></h2>
          <?php endif; ?>
          <div class="text-center mt-70 pb-30">
            <?php echo showAd(3); ?>

          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Author-single-area start -->

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/vendor/index.blade.php ENDPATH**/ ?>