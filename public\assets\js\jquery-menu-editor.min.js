function MenuEditor(e,t){var s=$("#"+e).data("level","0"),n={labelEdit:'<i class="fas fa-edit clickable"></i>',labelRemove:'<i class="fas fa-trash-alt clickable"></i>',textConfirmDelete:"This item will be deleted. Are you sure?",iconPicker:{cols:4,rows:4,footer:!1,iconset:"fontawesome5"},maxLevel:-1,listOptions:{hintCss:{border:"1px dashed #13981D"},opener:{as:"html",close:'<i class="fas fa-minus"></i>',open:'<i class="fas fa-plus"></i>',openerCss:{"margin-right":"10px",float:"none"},openerClass:"btn btn-success btn-sm"},placeholderCss:{"background-color":"gray"},ignoreClass:"clickable",listsClass:"pl-0",listsCss:{"padding-top":"10px"},complete:function(e){return MenuEditor.updateButtons(s),s.updateLevels(0),!0},isAllowed:function(e,t,s){return h(e,s)}}};$.extend(!0,n,t);var l=null,o=!0,i=null,r=null,a=n.iconPicker,c=(t=n.listOptions,$("#"+e+"_icon").iconpicker(a));function d(){i[0].reset(),(c=c.iconpicker(a)).iconpicker("setIcon","empty"),r.attr("disabled",!0),l=null}function p(e){return $("<a>").addClass(e.classCss).addClass("clickable").attr("href","#").html(e.text)}function u(){var e=$("<div>").addClass("btn-group float-right"),t=p({classCss:"btn btn-primary btn-sm btnEdit",text:n.labelEdit}),s=p({classCss:"btn btn-danger btn-sm btnRemove",text:n.labelRemove}),l=p({classCss:"btn btn-secondary btn-sm btnUp btnMove",text:'<i class="fas fa-angle-up clickable"></i>'}),o=p({classCss:"btn btn-secondary btn-sm btnDown btnMove",text:'<i class="fas fa-angle-down clickable"></i>'}),i=p({classCss:"btn btn-secondary btn-sm btnOut btnMove",text:'<i class="fas fa-level-down-alt clickable"></i>'}),r=p({classCss:"btn btn-secondary btn-sm btnIn btnMove",text:'<i class="fas fa-level-up-alt clickable"></i>'});return e.append(l).append(o).append(r).append(i).append(t).append(s),e}function f(e){$("<span>").addClass("sortableListsOpener "+t.opener.openerClass).css(t.opener.openerCss).on("mousedown touchstart",function(e){var s=$(this).closest("li");return s.hasClass("sortableListsClosed")?s.iconOpen(t):s.iconClose(t),!1}).prependTo(e.children("div").first()),e.hasClass("sortableListsOpen")?e.iconOpen(t):e.iconClose(t)}function h(e,t){if(n.maxLevel<0)return!0;var s=e.find("ul").length;return(0==t.length?0:parseInt(t.parent().data("level"))+1)+s<=n.maxLevel}s.sortableLists(n.listOptions),c.on("change",function(e){i.find("[name=icon]").val(e.icon)}),s.on("click",".btnRemove",function(t){if(t.preventDefault(),confirm(n.textConfirmDelete)){var l=$(this).closest("ul");$(this).closest("li").remove();var o=!1;void 0!==l.attr("id")&&(o=l.attr("id").toString()===e),l.children().length||o||(l.prev("div").children(".sortableListsOpener").first().remove(),l.remove()),MenuEditor.updateButtons(s)}}),s.on("click",".btnEdit",function(e){e.preventDefault(),function(e){var t=e.data();$.each(t,function(e,t){i.find("[name="+e+"]").val(t)}),i.find(".item-menu").first().focus(),t.hasOwnProperty("icon")?c.iconpicker("setIcon",t.icon):c.iconpicker("setIcon","empty");r.removeAttr("disabled"),"custom"!=t.type?$("#withUrl input[name='href']").parent(".form-group").hide():$("#withUrl input[name='href']").parent(".form-group").show()}(l=$(this).closest("li"))}),s.on("click",".btnUp",function(e){e.preventDefault();var t=$(this).closest("li");t.prev("li").before(t),MenuEditor.updateButtons(s)}),s.on("click",".btnDown",function(e){e.preventDefault();var t=$(this).closest("li");t.next("li").after(t),MenuEditor.updateButtons(s)}),s.on("click",".btnOut",function(e){e.preventDefault();var t=$(this).closest("ul"),n=$(this).closest("li");n.closest("ul").closest("li").after(n),t.children().length<=0&&(t.prev("div").children(".sortableListsOpener").first().remove(),t.remove()),MenuEditor.updateButtons(s),s.updateLevels()}),s.on("click",".btnIn",function(e){e.preventDefault();var t=$(this).closest("li"),n=t.prev("li");if(!h(t,n))return!1;if(n.length>0)if((l=n.children("ul")).length>0)l.append(t);else{var l=$("<ul>").addClass("pl-0").css("padding-top","10px");n.append(l),l.append(t),n.addClass("sortableListsOpen"),f(n)}MenuEditor.updateButtons(s),s.updateLevels()}),this.setForm=function(e){i=e},this.getForm=function(){return i},this.setUpdateButton=function(e){(r=e).attr("disabled",!0),l=null},this.getUpdateButton=function(){return r},this.getCurrentItem=function(){return l},this.update=function(){var e=this.getCurrentItem();if(null!==e){var t=e.data("icon");i.find(".item-menu").each(function(){e.data($(this).attr("name"),$(this).val())}),e.children().children("i").removeClass(t).addClass(e.data("icon")),e.find("span.txt").first().text(e.data("text")),d()}},this.add=function(){var e={};i.find(".item-menu").each(function(){e[$(this).attr("name")]=$(this).val()});var t=u(),n=$("<span>").addClass("txt").text(e.text),l=$("<i>").addClass(e.icon),o=$("<div>").css({overflow:"auto"}).append(l).append("&nbsp;").append(n).append(t),r=$("<li>").data(e);r.addClass("list-group-item pr-0").append(o),s.append(r),MenuEditor.updateButtons(s),d()},this.getString=function(){var e=s.sortableListsToJson();return JSON.stringify(e)},this.setData=function(e){var t=Array.isArray(e)?e:function(e){try{var t=JSON.parse(e)}catch(e){return null}return t}(e);if(null!==t){s.empty();var l=function e(t,n){var l=void 0===n?0:n,o=0===l?s:$("<ul>").addClass("pl-0").css("padding-top","10px").data("level",l);return $.each(t,function(t,s){var n=void 0!==s.children&&$.isArray(s.children),i={text:"",href:"",icon:"empty",target:"_self",title:""},r=$.extend({},s);n&&delete r.children,$.extend(i,r);var a=$("<li>").addClass("list-group-item pr-0");a.data(i);var c=$("<div>").css("overflow","auto"),d=$("<i>").addClass(s.icon),p=$("<span>").addClass("txt").append(s.text).css("margin-right","5px"),f=u();c.append(d).append("&nbsp;").append(p).append(f),a.append(c),n&&a.append(e(s.children,l+1)),o.append(a)}),o}(t);o?s.find("li").each(function(){var e=$(this);e.children("ul").length&&f(e)}):(l.sortableLists(n.listOptions),o=!0),MenuEditor.updateButtons(s)}}}!function(e){e.fn.sortableLists=function(t){var s=e("body").css("position","relative"),n={currElClass:"",placeholderClass:"",placeholderCss:{position:"relative",padding:0},hintClass:"",hintCss:{display:"none",position:"relative",padding:0},hintWrapperClass:"",hintWrapperCss:{},baseClass:"",baseCss:{position:"absolute",top:0-parseInt(s.css("margin-top")),left:0-parseInt(s.css("margin-left")),margin:0,padding:0,"z-index":2500},opener:{active:!1,open:"",close:"",openerCss:{float:"left",display:"inline-block","background-position":"center center","background-repeat":"no-repeat"},openerClass:""},listSelector:"ul",listsClass:"",listsCss:{},insertZone:50,insertZonePlus:!1,scroll:20,ignoreClass:"",isAllowed:function(e,t,s){return!0},onDragStart:function(e,t){return!0},onChange:function(e){return!0},complete:function(e){return!0}},l=e.extend(!0,{},n,t),o=e("<"+l.listSelector+" />").prependTo(s).attr("id","sortableListsBase").css(l.baseCss).addClass(l.listsClass+" "+l.baseClass),i=e("<li />").attr("id","sortableListsPlaceholder").css(l.placeholderCss).addClass(l.placeholderClass),r=e("<li />").attr("id","sortableListsHint").css(l.hintCss).addClass(l.hintClass),a=e("<"+l.listSelector+" />").attr("id","sortableListsHintWrapper").addClass(l.listsClass+" "+l.hintWrapperClass).css(l.listsCss).css(l.hintWrapperCss),c=e("<span />").addClass("sortableListsOpener "+l.opener.openerClass).css(l.opener.openerCss).on("mousedown touchstart",function(t){var s=e(this).closest("li");return s.hasClass("sortableListsClosed")?L(s):E(s),!1});"class"==l.opener.as?c.addClass(l.opener.close):"html"==l.opener.as?c.html(l.opener.close):console.error("Invalid setting for opener.as");var d={isDragged:!1,isRelEFP:null,oEl:null,rootEl:null,cEl:null,upScroll:!1,downScroll:!1,pX:0,pY:0,cX:0,cY:0,isAllowed:!0,e:{pageX:0,pageY:0,clientX:0,clientY:0},doc:e(document),win:e(window)};if(l.opener.active){if(!l.opener.open)throw"Opener.open value is not defined. It should be valid url, html or css class.";if(!l.opener.close)throw"Opener.close value is not defined. It should be valid url, html or css class.";e(this).find("li").each(function(){var t=e(this);t.children(l.listSelector).length&&(c.clone(!0).prependTo(t.children("div").first()),t.hasClass("sortableListsOpen")?L(t):E(t))})}return this.on("mousedown touchstart",function(t){var s=e(t.target);if(!(!1!==d.isDragged||l.ignoreClass&&s.hasClass(l.ignoreClass))){t.preventDefault(),"touchstart"===t.type&&v(t);var n=s.closest("li"),a=e(this);n[0]&&(l.onDragStart(t,n),function(t,s,n){d.isDragged=!0;var a=parseInt(s.css("margin-top")),c=parseInt(s.css("margin-bottom")),f=parseInt(s.css("margin-left")),h=parseInt(s.css("margin-right")),v=s.offset(),b=s.innerHeight();d.rootEl={el:n,offset:n.offset(),rootElClass:n.attr("class")},d.cEl={el:s,mT:a,mL:f,mB:c,mR:h,offset:v},d.cEl.xyOffsetDiff={X:t.pageX-d.cEl.offset.left,Y:t.pageY-d.cEl.offset.top},d.cEl.el.addClass("sortableListsCurrent "+l.currElClass),s.before(i);var g=d.placeholderNode=e("#sortableListsPlaceholder");s.css({width:s.width(),position:"absolute",top:v.top-a,left:v.left-f}).prependTo(o),g.css({display:"block",height:b}),r.css("height",b),d.doc.on("mousemove touchmove",p).on("mouseup touchend touchcancel",u)}(t,n,a))}});function p(t){if(d.isDragged){var s=d.cEl,n=d.doc,o=d.win;"touchmove"===t.type&&v(t),t.pageX||function(e){e.pageY=d.pY,e.pageX=d.pX,e.clientY=d.cY,e.clientX=d.cX}(t),n.scrollTop()>d.rootEl.offset.top-10&&t.clientY<50?d.upScroll?(t.pageY=t.pageY-l.scroll,e("html, body").each(function(t){e(this).scrollTop(e(this).scrollTop()-l.scroll)}),f(t)):function(e){if(d.upScroll)return;d.upScroll=setInterval(function(){d.doc.trigger("mousemove")},50)}():n.scrollTop()+o.height()<d.rootEl.offset.top+d.rootEl.el.outerHeight(!1)+10&&o.height()-t.clientY<50?d.downScroll?(t.pageY=t.pageY+l.scroll,e("html, body").each(function(t){e(this).scrollTop(e(this).scrollTop()+l.scroll)}),f(t)):function(e){if(d.downScroll)return;d.downScroll=setInterval(function(){d.doc.trigger("mousemove")},50)}():h(d),d.oElOld=d.oEl,s.el[0].style.visibility="hidden",d.oEl=oEl=function(t,s){if(!document.elementFromPoint)return null;var n=d.isRelEFP;if(null===n){var l,o;(l=d.doc.scrollTop())>0&&(n=null==(o=document.elementFromPoint(0,l+e(window).height()-1))||"HTML"==o.tagName.toUpperCase()),(l=d.doc.scrollLeft())>0&&(n=null==(o=document.elementFromPoint(l+e(window).width()-1,0))||"HTML"==o.tagName.toUpperCase())}n&&(t-=d.doc.scrollLeft(),s-=d.doc.scrollTop());var i=e(document.elementFromPoint(t,s));if(!d.rootEl.el.find(i).length)return null;if(i.is("#sortableListsPlaceholder")||i.is("#sortableListsHint"))return null;if(!i.is("li"))return(i=i.closest("li"))[0]?i:null;if(i.is("li"))return i}(t.pageX,t.pageY),s.el[0].style.visibility="visible",function(e,t){var s=t.oEl;if(!s||!t.oElOld)return;var n=s.outerHeight(!1),o=e.pageY-s.offset().top;l.insertZonePlus?14>o?g(e,s,7>o):n-14<o&&C(e,s,n-7<o):5>o?b(e,s):n-5<o&&m(e,s)}(t,d),function(e,t){var s=t.cEl;s.el.css({top:e.pageY-s.xyOffsetDiff.Y-s.mT,left:e.pageX-s.xyOffsetDiff.X-s.mL})}(t,d)}}function u(t){var s=d.cEl,n=e("#sortableListsHint",d.rootEl.el),o=r[0].style,i=null,a=!1,f=e("#sortableListsHintWrapper");"touchend"!==t.type&&"touchcancel"!==t.type||v(t),"block"==o.display&&n.length&&d.isAllowed?(i=n,a=!0):(i=d.placeholderNode,a=!1),offset=i.offset(),s.el.animate({left:offset.left-d.cEl.mL,top:offset.top-d.cEl.mT},250,function(){!function(e){var t=e.el[0].style;e.el.removeClass(l.currElClass+" sortableListsCurrent"),t.top="0",t.left="0",t.position="relative",t.width="auto"}(s),i.after(s.el[0]),i[0].style.display="none",o.display="none",n.remove(),f.removeAttr("id").removeClass(l.hintWrapperClass),f.length&&f.prev("div").prepend(c.clone(!0)),a?d.placeholderNode.slideUp(150,function(){d.placeholderNode.remove(),w(),l.onChange(s.el),l.complete(s.el),d.isDragged=!1}):(d.placeholderNode.remove(),w(),l.complete(s.el),d.isDragged=!1)}),h(d),d.doc.unbind("mousemove touchmove",p).unbind("mouseup touchend touchcancel",u)}function f(e){d.pY=e.pageY,d.pX=e.pageX,d.cY=e.clientY,d.cX=e.clientX}function h(e){clearInterval(e.upScroll),clearInterval(e.downScroll),e.upScroll=e.downScroll=!1}function v(e){e.pageX=e.originalEvent.changedTouches[0].pageX,e.pageY=e.originalEvent.changedTouches[0].pageY,e.screenX=e.originalEvent.changedTouches[0].screenX,e.screenY=e.originalEvent.changedTouches[0].screenY}function b(t,s){if(e("#sortableListsHintWrapper",d.rootEl.el).length&&r.unwrap(),t.pageX-s.offset().left<l.insertZone){if(s.prev("#sortableListsPlaceholder").length)return void r.css("display","none");s.before(r)}else{var n=s.children(),o=s.children(l.listSelector).first();if(o.children().first().is("#sortableListsPlaceholder"))return void r.css("display","none");o.length?o.prepend(r):(n.first().after(r),r.wrap(a)),d.oEl&&L(s)}r.css("display","block"),d.isAllowed=l.isAllowed(d.cEl.el,r,r.parents("li").first())}function g(t,s,n){if(e("#sortableListsHintWrapper",d.rootEl.el).length&&r.unwrap(),!n&&t.pageX-s.offset().left>l.insertZone){var o=s.children(),i=s.children(l.listSelector).first();if(i.children().first().is("#sortableListsPlaceholder"))return void r.css("display","none");i.length?i.prepend(r):(o.first().after(r),r.wrap(a)),d.oEl&&L(s)}else{if(s.prev("#sortableListsPlaceholder").length)return void r.css("display","none");s.before(r)}r.css("display","block"),d.isAllowed=l.isAllowed(d.cEl.el,r,r.parents("li").first())}function m(t,s){if(e("#sortableListsHintWrapper",d.rootEl.el).length&&r.unwrap(),t.pageX-s.offset().left<l.insertZone){if(s.next("#sortableListsPlaceholder").length)return void r.css("display","none");s.after(r)}else{var n=s.children(),o=s.children(l.listSelector).last();if(o.children().last().is("#sortableListsPlaceholder"))return void r.css("display","none");o.length?n.last().append(r):(s.append(r),r.wrap(a)),d.oEl&&L(s)}r.css("display","block"),d.isAllowed=l.isAllowed(d.cEl.el,r,r.parents("li").first())}function C(t,s,n){if(e("#sortableListsHintWrapper",d.rootEl.el).length&&r.unwrap(),!n&&t.pageX-s.offset().left>l.insertZone){var o=s.children(),i=s.children(l.listSelector).last();if(i.children().last().is("#sortableListsPlaceholder"))return void r.css("display","none");i.length?o.last().append(r):(s.append(r),r.wrap(a)),d.oEl&&L(s)}else{if(s.next("#sortableListsPlaceholder").length)return void r.css("display","none");s.after(r)}r.css("display","block"),d.isAllowed=l.isAllowed(d.cEl.el,r,r.parents("li").first())}function L(e){e.removeClass("sortableListsClosed").addClass("sortableListsOpen"),e.children(l.listSelector).css("display","block");var t=e.children("div").children(".sortableListsOpener").first();"html"==l.opener.as?t.html(l.opener.close):"class"==l.opener.as?t.addClass(l.opener.close).removeClass(l.opener.open):t.css("background-image","url("+l.opener.close+")")}function E(e){e.removeClass("sortableListsOpen").addClass("sortableListsClosed"),e.children(l.listSelector).css("display","none");var t=e.children("div").children(".sortableListsOpener").first();"html"==l.opener.as?t.html(l.opener.open):"class"==l.opener.as?t.addClass(l.opener.open).removeClass(l.opener.close):t.css("background-image","url("+l.opener.open+")")}function w(){e(l.listSelector,d.rootEl.el).each(function(t){e(this).children().length||(e(this).prev("div").children(".sortableListsOpener").first().remove(),e(this).remove())})}},e.fn.iconOpen=function(e){this.removeClass("sortableListsClosed").addClass("sortableListsOpen"),this.children("ul").css("display","block");var t=this.children("div").children(".sortableListsOpener").first();"html"===e.opener.as?t.html(e.opener.close):"class"===e.opener.as&&t.addClass(e.opener.close).removeClass(e.opener.open)},e.fn.iconClose=function(e){this.removeClass("sortableListsOpen").addClass("sortableListsClosed"),this.children("ul").css("display","none");var t=this.children("div").children(".sortableListsOpener").first();"html"===e.opener.as?t.html(e.opener.open):"class"===e.opener.as&&t.addClass(e.opener.open).removeClass(e.opener.close)},e.fn.sortableListsToJson=function(){var t=[];return e(this).children("li").each(function(){var s=e(this),n=s.data();t.push(n);var l=s.children("ul,ol").sortableListsToJson();l.length>0?n.children=l:delete n.children}),t},e.fn.updateLevels=function(t){var s=void 0===t?0:t;e(this).children("li").each(function(){var t=e(this).children("ul");t.length>0&&(t.data("level",s+1),t.updateLevels(s+1))})},e.fn.updateButtons=function(t){var s=void 0===t?0:t,n=["Up","In"],l=["Down"];0===s&&(n.push("Out"),l.push("Out"),e(this).children("li").hideButtons(["Out"])),e(this).children("li").each(function(){var t=e(this).children("ul");t.length>0&&t.updateButtons(s+1)}),e(this).children("li:first").hideButtons(n),e(this).children("li:last").hideButtons(l)},e.fn.hideButtons=function(t){for(var s=0;s<t.length;s++)e(this).find(".btn-group:first").children(".btn"+t[s]).hide()}}(jQuery),MenuEditor.updateButtons=function(e){e.find(".btnMove").show(),e.updateButtons()};
