<div class="header-top-bar">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-lg-7">
        <div class="top-left">
          <?php if(!empty($basicInfo->address)): ?>
            <span><i class="fas fa-map-marker-alt"></i> <?php echo e($basicInfo->address); ?></span>
          <?php endif; ?>

          <?php if(!empty($basicInfo->contact_number)): ?>
            <span><a href="<?php echo e('tel:' . $basicInfo->contact_number); ?>"><i
                  class="fas fa-phone"></i><?php echo e($basicInfo->contact_number); ?></a></span>
          <?php endif; ?>

          <?php if(!empty($basicInfo->email_address)): ?>
            <span><a href="<?php echo e('mailTo:' . $basicInfo->email_address); ?>"><i
                  class="fas fa-envelope"></i><?php echo e($basicInfo->email_address); ?></a></span>
          <?php endif; ?>
        </div>
      </div>

      <div class="col-lg-5">
        <div class="top-right">
          <ul class="d-flex align-items-center justify-content-end">
            <li>
              <div class="lang-dropdown">
                <div class="lang">
                  <img data-src="<?php echo e(asset('assets/img/languages.png')); ?>" alt="languages" width="25" class="lazy">
                </div>
                <form action="<?php echo e(route('change_language')); ?>" method="GET">
                  <select name="lang_code" onchange="this.form.submit()">
                    <?php $__currentLoopData = $allLanguageInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $languageInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e($languageInfo->code); ?>"
                        <?php echo e($languageInfo->code == $currentLanguageInfo->code ? 'selected' : ''); ?>>
                        <?php echo e($languageInfo->name); ?>

                      </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                </form>
              </div>
            </li>

            <li>
              <a href="<?php echo e(route('shop.cart')); ?>" class="cart-btn">
                <i class="fas fa-shopping-cart"></i><span id="product-count"><?php echo e(count($cartItemInfo)); ?></span>
              </a>
            </li>

            <li class="dropdown">
              <button class="dropdown-toggle" type="button" id="vendorDropdown" data-toggle="dropdown"
                aria-expanded="false">
                <?php echo e(__('Vendor')); ?>

              </button>
              <div
                class="dropdown-menu <?php if($currentLanguageInfo->direction == 1): ?> dropdown-menu-left <?php else: ?> dropdown-menu-right <?php endif; ?>"
                aria-labelledby="vendorDropdown">
                <?php if(auth()->guard('vendor')->guest()): ?>
                  <a class="dropdown-item" href="<?php echo e(route('vendor.login')); ?>"><?php echo e(__('Login')); ?></a>
                  <a class="dropdown-item" href="<?php echo e(route('vendor.signup')); ?>"><?php echo e(__('Signup')); ?></a>
                <?php endif; ?>
                <?php if(auth()->guard('vendor')->check()): ?>
                  <a class="dropdown-item" href="<?php echo e(route('vendor.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                  <a class="dropdown-item" href="<?php echo e(route('vendor.logout')); ?>"><?php echo e(__('Logout')); ?></a>
                <?php endif; ?>
              </div>
            </li>
            <li class="dropdown">
              <button class="dropdown-toggle" type="button" id="customerDropdown" data-toggle="dropdown"
                aria-expanded="false">
                <?php echo e(__('Customer')); ?>

              </button>
              <div
                class="dropdown-menu <?php if($currentLanguageInfo->direction == 1): ?> dropdown-menu-left <?php else: ?> dropdown-menu-right <?php endif; ?>"
                aria-labelledby="customerDropdown">
                <?php if(auth()->guard('web')->guest()): ?>
                  <a class="dropdown-item" href="<?php echo e(route('user.login')); ?>"><?php echo e(__('Login')); ?></a>
                  <a class="dropdown-item" href="<?php echo e(route('user.signup')); ?>"><?php echo e(__('Signup')); ?></a>
                <?php endif; ?>
                <?php if(auth()->guard('web')->check()): ?>
                  <a class="dropdown-item" href="<?php echo e(route('user.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
                  <a class="dropdown-item" href="<?php echo e(route('user.logout')); ?>"><?php echo e(__('Logout')); ?></a>
                <?php endif; ?>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/partials/header/header-top-v1.blade.php ENDPATH**/ ?>