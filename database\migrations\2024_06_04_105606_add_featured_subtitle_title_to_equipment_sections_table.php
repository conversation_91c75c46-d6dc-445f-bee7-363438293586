<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('equipment_sections', function (Blueprint $table) {
            $table->string('featured_section_title')->nullable();
            $table->string('featured_section_subtitle')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('equipment_sections', function (Blueprint $table) {
            $table->dropColumn(['featured_section_title','featured_section_subtitle']);
        });
    }
};
