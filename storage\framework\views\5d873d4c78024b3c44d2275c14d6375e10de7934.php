<?php if($footerSectionStatus == 1): ?>
    <footer class="footer-area footer-area-one bg_cover lazy"
        <?php if(!empty($basicInfo->footer_background_image)): ?> data-bg="<?php echo e(asset('assets/img/' . $basicInfo->footer_background_image)); ?>" <?php endif; ?>>
        <div class="footer-widget">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-4">
                        <div class="widget about-widget mb-40">
                            <?php if(!empty($basicInfo->footer_logo)): ?>
                                <a href="<?php echo e(route('index')); ?>" class="brand-logo">
                                    <img data-src="<?php echo e(asset('assets/img/' . $basicInfo->footer_logo)); ?>" alt="footer logo"
                                        class="lazy">
                                </a>
                            <?php endif; ?>

                            <p><?php echo e(!empty($footerInfo) ? $footerInfo->about_company : ''); ?></p>

                            <?php if(count($socialMediaInfos) > 0): ?>
                                <div class="social-box">
                                    <h5><?php echo e(__('Follow Us')); ?></h5>
                                    <ul class="social-link">
                                        <?php $__currentLoopData = $socialMediaInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $socialMediaInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <a href="<?php echo e($socialMediaInfo->url); ?>" target="_blank">
                                                    <i class="<?php echo e($socialMediaInfo->icon); ?>"></i>
                                                </a>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-4">
                        <div class="widget footer-widget-nav mb-40">
                            <h4 class="widget-title"><?php echo e(__('Useful Links')); ?></h4>

                            <?php if(count($quickLinkInfos) == 0): ?>
                                <h6 class="text-light"><?php echo e(__('No Link Found') . '!'); ?></h6>
                            <?php else: ?>
                                <ul class="widget-nav">
                                    <?php $__currentLoopData = $quickLinkInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quickLinkInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a href="<?php echo e($quickLinkInfo->url); ?>"><?php echo e($quickLinkInfo->title); ?></a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-4">
                        <div class="widget contact-info-widget mb-40">
                            <h4 class="widget-title"><?php echo e(__('Contact Us')); ?></h4>
                            <ul class="contact-info-list">
                                <?php if(!empty($basicInfo->email_address)): ?>
                                    <li>
                                        <div class="icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="info">
                                            <p><a
                                                    href="<?php echo e('mailTo:' . $basicInfo->email_address); ?>"><?php echo e($basicInfo->email_address); ?></a>
                                            </p>
                                        </div>
                                    </li>
                                <?php endif; ?>

                                <?php if(!empty($basicInfo->contact_number)): ?>
                                    <li>
                                        <div class="icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="info">
                                            <p><a
                                                    href="<?php echo e('tel:' . $basicInfo->contact_number); ?>"><?php echo e($basicInfo->contact_number); ?></a>
                                            </p>
                                        </div>
                                    </li>
                                <?php endif; ?>

                                <?php if(!empty($basicInfo->address)): ?>
                                    <li>
                                        <div class="icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="info">
                                            <p><?php echo e($basicInfo->address); ?></p>
                                        </div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if(!empty($footerInfo)): ?>
            <div class="copyright-area">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="copyright-text text-center">
                                <p><?php echo e($footerInfo->copyright_text); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </footer>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/partials/footer/footer-v1.blade.php ENDPATH**/ ?>