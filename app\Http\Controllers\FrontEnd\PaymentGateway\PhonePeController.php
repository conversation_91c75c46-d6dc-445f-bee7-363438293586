<?php

namespace App\Http\Controllers\FrontEnd\PaymentGateway;

use App\Models\Vendor;
use App\Models\Earning;
use App\Models\Commission;
use App\Models\Shop\Product;
use Illuminate\Http\Request;
use Ixudra\Curl\Facades\Curl;

use App\Http\Controllers\Controller;
use App\Models\Instrument\Equipment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\PaymentGateway\OnlineGateway;
use App\Http\Controllers\FrontEnd\Shop\PurchaseProcessController;
use App\Http\Controllers\FrontEnd\Instrument\BookingProcessController;

class PhonePeController extends Controller
{
    public function index(Request $request, $paymentFor)
    {
        $currencyInfo = $this->getCurrencyInfo();
        if ($currencyInfo->base_currency_text != 'INR') {
            return redirect()->back()->with('warning', 'Invalid currency for PhonePe payment.');
        }

        if ($paymentFor == 'product purchase') {
            // get the products from session
            if ($request->session()->has('productCart')) {
                $productList = $request->session()->get('productCart');
            } else {
                Session::flash('error', 'Something went wrong!');
                return redirect()->route('shop.products');
            }

            $purchaseProcess = new PurchaseProcessController();

            // do calculation
            $calculatedData = $purchaseProcess->calculation($request, $productList);
        } elseif ($paymentFor == 'equipment booking') {
            // check whether the equipment lowest price exist or not in session
            if (!$request->session()->has('totalPrice')) {
                Session::flash('error', 'Something went wrong!');
                return redirect()->route('all_equipment');
            }
            $bookingProcess = new BookingProcessController();
            // do calculation
            $calculatedData = $bookingProcess->calculation($request);
        }

        if ($paymentFor == 'product purchase') {
            $arrData = [
                'billingFirstName' => $request['billing_first_name'],
                'billingLastName' => $request['billing_last_name'],
                'billingEmail' => $request['billing_email'],
                'billingContactNumber' => $request['billing_contact_number'],
                'billingAddress' => $request['billing_address'],
                'billingCity' => $request['billing_city'],
                'billingState' => $request['billing_state'],
                'billingCountry' => $request['billing_country'],
                'shippingFirstName' => $request['shipping_first_name'],
                'shippingLastName' => $request['shipping_last_name'],
                'shippingEmail' => $request['shipping_email'],
                'shippingContactNumber' => $request['shipping_contact_number'],
                'shippingAddress' => $request['shipping_address'],
                'shippingCity' => $request['shipping_city'],
                'shippingState' => $request['shipping_state'],
                'shippingCountry' => $request['shipping_country'],
                'total' => $calculatedData['total'],
                'discount' => $calculatedData['discount'],
                'productShippingChargeId' => $request->exists('charge_id') ? $request['charge_id'] : null,
                'shippingCharge' => $calculatedData['shippingCharge'],
                'tax' => $calculatedData['tax'],
                'grandTotal' => $calculatedData['grandTotal'],
                'currencyText' => $currencyInfo->base_currency_text,
                'currencyTextPosition' => $currencyInfo->base_currency_text_position,
                'currencySymbol' => $currencyInfo->base_currency_symbol,
                'currencySymbolPosition' => $currencyInfo->base_currency_symbol_position,
                'paymentMethod' => 'PhonePe',
                'gatewayType' => 'online',
                'paymentStatus' => 'completed',
                'orderStatus' => 'pending',
            ];
            $title = 'Purchase Product';
            $notifyURL = route('shop.phonepe.response');
        } elseif ($paymentFor == 'equipment booking') {
            // get start & end date
            $dates = $bookingProcess->getDates($request['dates']);

            // get location name
            $location_name = $bookingProcess->getLocation($request['location']);

            $arrData = [
                'name' => $request['name'],
                'contactNumber' => $request['contact_number'],
                'email' => $request['email'],
                'equipmentId' => $request['equipment_id'],
                'startDate' => $dates['startDate'],
                'endDate' => $dates['endDate'],
                'shippingMethod' => $request->filled('shipping_method') ? $request['shipping_method'] : null,
                'location' => $location_name,
                'total' => $calculatedData['total'],
                'discount' => $calculatedData['discount'],
                'shippingCost' => $calculatedData['shippingCharge'],
                'tax' => $calculatedData['tax'],
                'grandTotal' => $calculatedData['grandTotal'],
                'security_deposit_amount' => $calculatedData['security_deposit_amount'],
                'currencySymbol' => $currencyInfo->base_currency_symbol,
                'currencySymbolPosition' => $currencyInfo->base_currency_symbol_position,
                'currencyText' => $currencyInfo->base_currency_text,
                'currencyTextPosition' => $currencyInfo->base_currency_text_position,
                'paymentMethod' => 'PhonePe',
                'gatewayType' => 'online',
                'paymentStatus' => 'completed',
                'shippingStatus' => !$request->filled('shipping_method') ? null : 'pending',
            ];

            $title = 'Equipment Booking';
            $notifyURL = route('equipment.phonepe.response');
        }

        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        ~~~~~~~~~~~~~~~~~ Payment Gateway Info ~~~~~~~~~~~~~~
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

        // put some data in session before redirect to paypal url
        $request->session()->forget('paymentFor');
        $request->session()->put('paymentFor', $paymentFor);
        $request->session()->put('arrData', $arrData);

        $info = OnlineGateway::where('keyword', 'phonepe')->first();
        $information = json_decode($info->information, true);
        $randomNo = substr(uniqid(), 0, 3);
        $data = [
            // 'merchantId' => 'M22ZG63B00XON', // prod merchant id
            'merchantId' => $information['phonepe_merchant_id'], // sandbox merchant id
            'merchantTransactionId' => uniqid(),
            'merchantUserId' => 'MUID' . $randomNo, // it will be the ID of tenants / vendors from database
            'amount' => round($calculatedData['grandTotal'], 2) * 100,
            'redirectUrl' => $notifyURL,
            'redirectMode' => 'POST',
            'callbackUrl' => $notifyURL,
            'mobileNumber' => $request['billing_contact_number'] ?? $request['contact_number'],
            'paymentInstrument' => [
                'type' => 'PAY_PAGE',
            ],
        ];

        $encode = base64_encode(json_encode($data));

        $saltKey = $information['salt_key']; // sandbox salt key
        $saltIndex = $information['salt_index'];

        $string = $encode . '/pg/v1/pay' . $saltKey;
        $sha256 = hash('sha256', $string);

        $finalXHeader = $sha256 . '###' . $saltIndex;

        if ($information['phonepe_sandbox_status'] == 1) {
            $url = 'https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay'; // sandbox payment URL
        } else {
            $url = 'https://api.phonepe.com/apis/hermes/pg/v1/pay'; // prod payment URL
        }

        $response = Curl::to($url)
            ->withHeader('Content-Type:application/json')
            ->withHeader('X-VERIFY:' . $finalXHeader)
            ->withData(json_encode(['request' => $encode]))
            ->post();

        $rData = json_decode($response);

        if ($rData->success == true) {
            if (!empty($rData->data->instrumentResponse->redirectInfo->url)) {
                $request->session()->put('arrData', $arrData);
                return redirect()->to($rData->data->instrumentResponse->redirectInfo->url);
            } else {
                return redirect()->back()->with('error', 'Payment Canceled.');
            }
        } else {
            return redirect()->back()->with('error', 'Payment Canceled.');
        }
        /* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        ~~~~~~~~~~~~~~~~~ Payment Gateway Info End ~~~~~~~~~~~~~~
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

        return redirect()->to($rData->data->instrumentResponse->redirectInfo->url);

        // You can then access the response using $response->body() or other methods.
    }

    public function response(Request $request)
    {
        $input = $request->all();
        $paymentPurpose = $request->session()->get('paymentFor');
        if ($paymentPurpose == 'product purchase') {
            $productList = $request->session()->get('productCart');
        }
        $arrData = $request->session()->get('arrData');
        if ($input['code'] == 'PAYMENT_SUCCESS') {
            // remove this session datas
            $request->session()->forget('paymentFor');
            $request->session()->forget('arrData');
            $request->session()->forget('paymentId');

            if ($paymentPurpose == 'product purchase') {
                $purchaseProcess = new PurchaseProcessController();

                // store product order information in database
                $orderInfo = $purchaseProcess->storeData($productList, $arrData);

                //add blance to admin revinue
                $earning = Earning::first();

                $earning->total_revenue = $earning->total_revenue + $orderInfo->grand_total;
                $earning->total_earning = $earning->total_earning + $orderInfo->grand_total;
                $earning->save();

                $transactionStoreArr = [
                    'transcation_id' => time(),
                    'booking_id' => $orderInfo->id,
                    'transcation_type' => 5,
                    'user_id' => null,
                    'vendor_id' => null,
                    'payment_status' => 1,
                    'payment_method' => $orderInfo->gateway_type,
                    'grand_total' => $orderInfo->grand_total,
                    'pre_balance' => null,
                    'after_balance' => null,
                    'gateway_type' => $orderInfo->gateway_type,
                    'currency_symbol' => $orderInfo->currency_symbol,
                    'currency_symbol_position' => $orderInfo->currency_symbol_position,
                ];

                storeTranscation($transactionStoreArr);

                // then subtract each product quantity from respective product stock
                foreach ($productList as $key => $item) {
                    $product = Product::query()->find($key);

                    if ($product->product_type == 'physical') {
                        $stock = $product->stock - intval($item['quantity']);

                        $product->update(['stock' => $stock]);
                    }
                }

                // generate an invoice in pdf format
                $invoice = $purchaseProcess->generateInvoice($orderInfo, $productList);

                // then, update the invoice field info in database
                $orderInfo->update(['invoice' => $invoice]);

                // send a mail to the customer with the invoice
                $purchaseProcess->prepareMail($orderInfo, $transactionStoreArr['transcation_id']);

                // remove all session data
                $request->session()->forget('productCart');
                $request->session()->forget('discount');

                return redirect()->route('shop.purchase_product.complete');
            } elseif ($paymentPurpose == 'equipment booking') {
                $bookingProcess = new BookingProcessController();

                // store equipment booking information in database
                $bookingInfo = $bookingProcess->storeData($arrData);

                // generate an invoice in pdf format
                $invoice = $bookingProcess->generateInvoice($bookingInfo);
                //calculate commission start

                $equipment = Equipment::findOrFail($arrData['equipmentId']);
                if (!empty($equipment)) {
                    if ($equipment->vendor_id != null) {
                        $vendor_id = $equipment->vendor_id;
                    } else {
                        $vendor_id = null;
                    }
                } else {
                    $vendor_id = null;
                }
                //calculate commission
                $percent = Commission::select('equipment_commission')->first();

                $commission = (($bookingInfo->total - $bookingInfo->discount) * $percent->equipment_commission) / 100;

                //get vendor
                $vendor = Vendor::where('id', $bookingInfo->vendor_id)->first();

                //add blance to admin revinue
                $earning = Earning::first();

                $earning->total_revenue = $earning->total_revenue + $bookingInfo->grand_total;
                if ($vendor) {
                    $earning->total_earning = $earning->total_earning + $commission + $bookingInfo->tax;
                } else {
                    $earning->total_earning = $earning->total_earning + ($bookingInfo->grand_total - $bookingInfo->security_deposit_amount);
                }
                $earning->save();

                //store Balance  to vendor
                if ($vendor) {
                    $pre_balance = $vendor->amount;
                    $vendor->amount = $vendor->amount + ($bookingInfo->grand_total - ($commission + $bookingInfo->tax + $bookingInfo->security_deposit_amount));
                    $vendor->save();
                    $after_balance = $vendor->amount;

                    $received_amount = $bookingInfo->grand_total - ($commission + $bookingInfo->tax + $bookingInfo->security_deposit_amount);

                    // then, update the invoice field info in database
                    $bookingInfo->update([
                        'invoice' => $invoice,
                        'comission' => $commission,
                        'received_amount' => $received_amount,
                    ]);
                } else {
                    // then, update the invoice field info in database
                    $bookingInfo->update([
                        'invoice' => $invoice,
                    ]);
                    $received_amount = $bookingInfo->grand_total - ($bookingInfo->security_deposit_amount + $bookingInfo->tax);
                    $after_balance = null;
                    $pre_balance = null;
                }
                //calculate commission end

                if (!is_null($vendor_id)) {
                    $comission = $bookingInfo->comission;
                } else {
                    $comission = $bookingInfo->grand_total - ($bookingInfo->security_deposit_amount + $bookingInfo->tax);
                }

                //store data to transcation table
                $transactionStoreArr = [
                    'transcation_id' => time(),
                    'booking_id' => $bookingInfo->id,
                    'transcation_type' => 1,
                    'user_id' => Auth::guard('web')->check() == true ? Auth::guard('web')->user()->id : null,
                    'vendor_id' => $vendor_id,
                    'payment_status' => 1,
                    'payment_method' => $bookingInfo->payment_method,
                    'shipping_charge' => $bookingInfo->shipping_cost,
                    'commission' => $comission,
                    'security_deposit' => $bookingInfo->security_deposit_amount,
                    'tax' => $bookingInfo->tax,
                    'grand_total' => $received_amount,
                    'pre_balance' => $pre_balance,
                    'after_balance' => $after_balance,
                    'gateway_type' => $bookingInfo->gateway_type,
                    'currency_symbol' => $bookingInfo->currency_symbol,
                    'currency_symbol_position' => $bookingInfo->currency_symbol_position,
                ];

                storeTranscation($transactionStoreArr);

                // send a mail to the customer with the invoice
                $bookingProcess->prepareMail($bookingInfo, $transactionStoreArr['transcation_id']);

                // remove all session data
                $request->session()->forget('totalPrice');
                $request->session()->forget('equipmentDiscount');

                return redirect()->route('equipment.make_booking.complete');
            }
        } else {
            $request->session()->forget('paymentFor');
            $request->session()->forget('arrData');

            if ($paymentPurpose == 'product purchase') {
                // remove session data
                $request->session()->forget('productCart');
                $request->session()->forget('discount');
                return redirect()->route('shop.purchase_product.cancel');
            } elseif ($paymentPurpose == 'equipment booking') {
                // remove session data
                $request->session()->forget('totalPrice');
                $request->session()->forget('equipmentDiscount');
                return redirect()->route('equipment.make_booking.cancel');
            }
        }
    }
}
