[2024-08-07 17:18:28] local.ERROR: include(/Users/<USER>/Sites/MultiRent/multirent/vendor/composer/../rachidlaasri/laravel-installer/src/Providers/LaravelInstallerServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(/Users/<USER>/Sites/MultiRent/multirent/vendor/composer/../rachidlaasri/laravel-installer/src/Providers/LaravelInstallerServiceProvider.php): Failed to open stream: No such file or directory at /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php:571)
[stacktrace]
#0 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(/Users/<USER>', '/Users/<USER>', 571)
#1 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(/Users/<USER>', '/Users/<USER>', 571)
#2 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(571): include('/Users/<USER>')
#3 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(428): Composer\\Autoload\\includeFile('/Users/<USER>')
#4 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(750): Composer\\Autoload\\ClassLoader->loadClass('RachidLaasri\\\\La...')
#5 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(684): Illuminate\\Foundation\\Application->resolveProvider('RachidLaasri\\\\La...')
#6 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('RachidLaasri\\\\La...')
#7 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(664): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(242): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2024-08-07 17:18:28] local.ERROR: include(/Users/<USER>/Sites/MultiRent/multirent/vendor/composer/../rachidlaasri/laravel-installer/src/Providers/LaravelInstallerServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(/Users/<USER>/Sites/MultiRent/multirent/vendor/composer/../rachidlaasri/laravel-installer/src/Providers/LaravelInstallerServiceProvider.php): Failed to open stream: No such file or directory at /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php:571)
[stacktrace]
#0 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(/Users/<USER>', '/Users/<USER>', 571)
#1 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(/Users/<USER>', '/Users/<USER>', 571)
#2 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(571): include('/Users/<USER>')
#3 /Users/<USER>/Sites/MultiRent/multirent/vendor/composer/ClassLoader.php(428): Composer\\Autoload\\includeFile('/Users/<USER>')
#4 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(750): Composer\\Autoload\\ClassLoader->loadClass('RachidLaasri\\\\La...')
#5 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(684): Illuminate\\Foundation\\Application->resolveProvider('RachidLaasri\\\\La...')
#6 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register('RachidLaasri\\\\La...')
#7 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(664): Illuminate\\Foundation\\ProviderRepository->load(Array)
#8 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#9 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(242): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 /Users/<USER>/Sites/MultiRent/multirent/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-07-23 16:15:14] local.ERROR: Cannot declare class Config\Iyzipay, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class Config\\Iyzipay, because the name is already in use at C:\\xampp\\htdocs\\agapeconnect\\config\\Iyzico.php:5)
[stacktrace]
#0 {main}
"} 
[2025-07-23 16:17:09] local.ERROR: Cannot declare class Config\IyzicoConfig, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class Config\\IyzicoConfig, because the name is already in use at C:\\xampp\\htdocs\\agapeconnect\\config\\Iyzico.php:5)
[stacktrace]
#0 {main}
"} 
[2025-07-23 16:20:56] local.ERROR: Unable to prepare route [shop/purchase-product/callback] for serialization. Another route has already been assigned name [xendit.callback]. {"exception":"[object] (LogicException(code: 0): Unable to prepare route [shop/purchase-product/callback] for serialization. Another route has already been assigned name [xendit.callback]. at C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php:247)
[stacktrace]
#0 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(208): Illuminate\\Routing\\AbstractRouteCollection->addToSymfonyRoutesCollection(Object(Symfony\\Component\\Routing\\RouteCollection), Object(Illuminate\\Routing\\Route))
#1 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(246): Illuminate\\Routing\\AbstractRouteCollection->toSymfonyRouteCollection()
#2 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(192): Illuminate\\Routing\\RouteCollection->toSymfonyRouteCollection()
#3 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(163): Illuminate\\Routing\\AbstractRouteCollection->dumper()
#4 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteCacheCommand.php(120): Illuminate\\Routing\\AbstractRouteCollection->compile()
#5 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteCacheCommand.php(79): Illuminate\\Foundation\\Console\\RouteCacheCommand->buildRouteCacheFile(Object(Illuminate\\Routing\\RouteCollection))
#6 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteCacheCommand->handle()
#7 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#12 C:\\xampp\\htdocs\\agapeconnect\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\xampp\\htdocs\\agapeconnect\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\xampp\\htdocs\\agapeconnect\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteCacheCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\xampp\\htdocs\\agapeconnect\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\xampp\\htdocs\\agapeconnect\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\xampp\\htdocs\\agapeconnect\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
