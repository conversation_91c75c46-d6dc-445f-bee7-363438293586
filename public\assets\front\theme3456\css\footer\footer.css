/* -----------------------------------------
	Footer CSS
----------------------------------------- */
.footer-area {
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.footer-area :is(p, .footer-widget a, span) {
  color: var(--text-light);
}
.footer-area .footer-top {
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.footer-area .footer-widget {
  margin-bottom: 30px;
}
.footer-area .footer-widget ul {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.footer-area .footer-widget .title {
  color: var(--text-white);
  text-transform: capitalize;
  margin-bottom: 25px;
}
@media (max-width: 991.98px) {
  .footer-area .footer-widget .title {
    margin-bottom: 15px;
  }
}
.footer-area .footer-widget p {
  margin-bottom: 25px;
}
.footer-area .info-list li,
.footer-area .footer-links li {
  position: relative;
  overflow: hidden;
  font-size: var(--font-sm);
}
.footer-area .info-list li:not(:last-child),
.footer-area .footer-links li:not(:last-child) {
  margin-bottom: 10px;
}
.footer-area .info-list li a,
.footer-area .footer-links li a {
  display: block;
}
.footer-area .info-list li a:hover,
.footer-area .footer-links li a:hover {
  color: var(--color-primary);
}
.footer-area .footer-links a {
  position: relative;
  overflow: hidden;
  display: block;
  transition: all 0.3s linear;
}
.footer-area .copy-right-area {
  border-top: 1px dashed rgba(var(--color-white-rgb), 0.3);
  text-align: center;
}
.footer-area.footer-2 :is(p, .footer-widget a, span) {
  color: var(--text-medium);
}
.footer-area.footer-2 .footer-widget .title {
  color: var(--text-dark);
}
.footer-area.footer-2 .copy-right-area {
  border-top: 1px dashed rgba(var(--color-dark-rgb), 0.2);
}
