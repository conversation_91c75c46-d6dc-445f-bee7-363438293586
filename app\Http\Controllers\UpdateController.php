<?php

namespace App\Http\Controllers;

use App\Models\PaymentGateway\OnlineGateway;
use Illuminate\Http\Request;

class UpdateController extends Controller
{
    public function addPaymentGateways()
    {
        $information['key'] = '';
        $gatewaysData = [
            ['name' => 'Midtrans', 'keyword' => 'midtrans', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Iyzico', 'keyword' => 'iyzico', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Paytabs', 'keyword' => 'paytabs', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Toyyibpay', 'keyword' => 'toyyibpay', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Phonepe', 'keyword' => 'phonepe', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Yoco', 'keyword' => 'yoco', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Myfatoorah', 'keyword' => 'myfatoorah', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Xendit', 'keyword' => 'xendit', 'information' => json_encode($information), 'status' => 1],
            ['name' => 'Perfect Money', 'keyword' => 'perfect_money', 'information' => json_encode($information), 'status' => 1],
            // Add more gateways as needed
        ];

        foreach ($gatewaysData as $gatewayData) {
            OnlineGateway::create($gatewayData);
        }

        return 'New Gateways added successfully!';
    }

    public function index()
    {
        // run add new payments function
        $this->addPaymentGateways();
    }
}
