<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Xendit Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Xendit payment gateway
    |
    */

    'secret_key' => env('XENDIT_SECRET_KEY', ''),
    'public_key' => env('XENDIT_PUBLIC_KEY', ''),

    // Base64 encoded secret key for API authentication
    'key_auth' => base64_encode(env('XENDIT_SECRET_KEY', '') . ':'),

    // API URLs
    'api_url' => env('XENDIT_API_URL', 'https://api.xendit.co'),

    // Supported currencies
    'supported_currencies' => ['IDR', 'PHP', 'USD', 'SGD', 'MYR'],

    // Webhook settings
    'webhook_token' => env('XENDIT_WEBHOOK_TOKEN', ''),

    // Payment methods
    'payment_methods' => [
        'credit_card' => true,
        'bank_transfer' => true,
        'ewallet' => true,
        'virtual_account' => true,
        'retail_outlet' => true,
        'qr_code' => true,
    ],
];
