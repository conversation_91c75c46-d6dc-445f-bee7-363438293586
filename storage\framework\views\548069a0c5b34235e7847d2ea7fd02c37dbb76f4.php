<?php $__env->startSection('pageHeading'); ?>
    <?php echo e(__('Home')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('metaKeywords'); ?>
    <?php if(!empty($seoInfo)): ?>
        <?php echo e($seoInfo->meta_keyword_home); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('metaDescription'); ?>
    <?php if(!empty($seoInfo)): ?>
        <?php echo e($seoInfo->meta_description_home); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!--====== Start Hero Section ======-->
    <?php if(count($sliderInfos) > 0): ?>
        <section class="hero-area">
            <div class="hero-slider-one">
                <?php $__currentLoopData = $sliderInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sliderInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="single-hero-slider bg_cover lazy"
                        data-bg="<?php echo e(asset('assets/img/hero/sliders/' . $sliderInfo->background_image)); ?>">
                        <div class="container">
                            <div class="row justify-content-center">
                                <div class="col-lg-12">
                                    <div class="hero-content text-center">
                                        <h1><?php echo e(!empty($sliderInfo->title) ? $sliderInfo->title : ''); ?></h1>
                                        <p><?php echo e(!empty($sliderInfo->text) ? $sliderInfo->text : ''); ?></p>

                                        <div class="hero-search-wrapper">
                                            <form action="<?php echo e(route('all_equipment')); ?>" method="GET">
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="form_group">
                                                            <input type="text" class="form_control"
                                                                placeholder="<?php echo e(__('What are you looking for?')); ?>"
                                                                name="keyword">
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-3">
                                                        <div class="form_group">
                                                            <div class="input-wrap">
                                                                <input type="text" class="form_control" id="date-range"
                                                                    placeholder="<?php echo e(__('Search By Date')); ?>" name="dates"
                                                                    readonly>
                                                                <i class="far fa-calendar-alt"></i>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-2">
                                                        <div class="form_group">
                                                            <select class="form_control" name="category">
                                                                <option selected disabled><?php echo e(__('Categories')); ?></option>

                                                                <?php $__currentLoopData = $equipCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($category->slug); ?>">
                                                                        <?php echo e($category->name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-2">
                                                        <div class="form_group">
                                                            <input type="text" class="form_control"
                                                                placeholder="<?php echo e(__('Location')); ?>" name="location">
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-2">
                                                        <div class="form_group">
                                                            <button type="submit"
                                                                class="search-btn"><?php echo e(__('Search')); ?></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Hero Section ======-->

    <!--====== Start About Section ======-->
    <?php if($secInfo->about_section_status == 1): ?>
        <section class="about-area pt-130 pb-120">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-7">
                        <?php if(!empty($aboutSectionImage)): ?>
                            <div class="about-img-box about-img-box-one mb-50">
                                <div class="about-img about-img-one">
                                    <img data-src="<?php echo e(asset('assets/img/' . $aboutSectionImage)); ?>" alt="about image"
                                        class="lazy">
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-lg-5">
                        <div class="about-content-box about-content-box-one mb-50">
                            <div class="section-title mb-50">
                                <span
                                    class="sub-title"><?php echo e(!empty($aboutSecInfo->subtitle) ? $aboutSecInfo->subtitle : ''); ?></span>
                                <h2><?php echo e(!empty($aboutSecInfo->title) ? $aboutSecInfo->title : ''); ?></h2>
                            </div>
                            <p><?php echo !empty($aboutSecInfo->text) ? nl2br($aboutSecInfo->text) : ''; ?></p>

                            <?php if(!empty($aboutSecInfo->button_name) && !empty($aboutSecInfo->button_url)): ?>
                                <a href="<?php echo e($aboutSecInfo->button_url); ?>"
                                    class="main-btn mt-25"><?php echo e($aboutSecInfo->button_name); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="mt-40 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End About Section ======-->

    <!--====== Start Working Process Section ======-->
    <?php if($secInfo->work_process_section_status == 1): ?>
        <section class="working-process light-gray pt-130 pb-120">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="section-title text-center mb-85">
                            <span
                                class="sub-title"><?php echo e(!empty($workProcessSecInfo->subtitle) ? $workProcessSecInfo->subtitle : ''); ?></span>
                            <h2><?php echo e(!empty($workProcessSecInfo->title) ? $workProcessSecInfo->title : ''); ?></h2>
                            <p><?php echo e(!empty($workProcessSecInfo->text) ? $workProcessSecInfo->text : ''); ?></p>
                        </div>
                    </div>
                </div>

                <?php if(count($processes) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Work Process Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row justify-content-between">
                        <?php $__currentLoopData = $processes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $process): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-2 col-md-3 process-column">
                                <div class="process-item process-item-one mb-30">
                                    <div class="count-box">
                                        <div class="icon">
                                            <i class="<?php echo e($process->icon); ?>"></i>
                                        </div>
                                        <div class="process-count">
                                            <?php echo e(str_pad($loop->index + 1,2, '0', STR_PAD_LEFT)); ?></div>
                                    </div>

                                    <div class="content text-center">
                                        <h4><?php echo e($process->title); ?></h4>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <div class="mt-70 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Working Process Section ======-->

    <!--====== Start Features Section ======-->
    <?php if($secInfo->feature_section_status == 1): ?>
        <section class="features-area pt-130 pb-120">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="section-title text-center mb-55">
                            <span
                                class="sub-title"><?php echo e(!empty($featureSecInfo->subtitle) ? $featureSecInfo->subtitle : ''); ?></span>
                            <h2><?php echo e(!empty($featureSecInfo->title) ? $featureSecInfo->title : ''); ?></h2>
                            <p><?php echo e(!empty($featureSecInfo->text) ? $featureSecInfo->text : ''); ?></p>
                        </div>
                    </div>
                </div>

                <?php if(count($features) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Feature Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-sm-12">
                                <div class="features-item features-item-one mb-40">
                                    <div class="icon">
                                        <i class="<?php echo e($feature->icon); ?>"></i>
                                    </div>
                                    <div class="content">
                                        <h4><?php echo e($feature->title); ?></h4>
                                        <p><?php echo e($feature->text); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <div class="mt-50 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Features Section ======-->

    <!--====== Start Counter Section ======-->
    <?php if($secInfo->counter_section_status == 1): ?>
        <section class="counter-area bg-with-overlay bg_cover pt-130 pb-90 lazy"
            <?php if(!empty($counterSectionInfo->counter_section_image)): ?> data-bg="<?php echo e(asset('assets/img/' . $counterSectionInfo->counter_section_image)); ?>" <?php endif; ?>>
            <div class="container">
                <?php if(count($counters) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3 class="text-light"><?php echo e(__('No Information Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php $__currentLoopData = $counters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $counter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-3 col-md-6 col-sm-12 counter-column">
                                <div class="counter-item counter-item-one mb-40 text-center">
                                    <div class="icon">
                                        <i class="<?php echo e($counter->icon); ?>"></i>
                                    </div>
                                    <div class="content">
                                        <h2><span class="count"><?php echo e($counter->amount); ?></span>+</h2>
                                        <h5><?php echo e($counter->title); ?></h5>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Counter Section ======-->

    <!--====== Start Featured Equipment Section ======-->
    <?php if($secInfo->equipment_section_status == 1): ?>
        <section class="pricing-area pt-120 pb-110">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="section-title text-center mb-50">
                            <span
                                class="sub-title"><?php echo e(!empty($equipmentSecInfo->subtitle) ? $equipmentSecInfo->subtitle : ''); ?></span>
                            <h2><?php echo e(!empty($equipmentSecInfo->title) ? $equipmentSecInfo->title : ''); ?></h2>
                            <p><?php echo e(!empty($equipmentSecInfo->text) ? $equipmentSecInfo->text : ''); ?></p>
                        </div>
                    </div>
                </div>

                <?php if(count($allEquipment) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Equipment Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="equipment-slider">
                        <?php $__currentLoopData = $allEquipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="slider-item">
                                <div class="pricing-item pricing-item-one mb-40">
                                    <div class="pricing-img">
                                        <a href="<?php echo e(route('equipment_details', ['slug' => $equipment->slug])); ?>"
                                            class="d-block">
                                            <img data-src="<?php echo e(asset('assets/img/equipments/thumbnail-images/' . $equipment->thumbnail_image)); ?>"
                                                alt="image" class="lazy">
                                        </a>
                                    </div>

                                    <?php
                                        $position = $currencyInfo->base_currency_symbol_position;
                                        $symbol = $currencyInfo->base_currency_symbol;
                                    ?>

                                    <div class="pricing-info">
                                        <div class="price-info">
                                            <h5><?php echo e(__('Price')); ?></h5>

                                            <?php if(!empty($equipment->lowest_price)): ?>
                                                <span><?php echo e(__('Starts From')); ?></span>
                                            <?php endif; ?>

                                            <div class="price-tag">
                                                <?php if(!empty($equipment->lowest_price)): ?>
                                                    <h4>
                                                        <?php echo e($position == 'left' ? $symbol : ''); ?><?php echo e($equipment->lowest_price); ?><?php echo e($position == 'right' ? $symbol : ''); ?>

                                                    </h4>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="pricing-body">
                                            <h5 class="title mb-0">
                                                <a href="<?php echo e(route('equipment_details', ['slug' => $equipment->slug])); ?>">
                                                    <?php echo e(strlen($equipment->title) > 25 ? mb_substr($equipment->title, 0, 25, 'UTF-8') . '...' : $equipment->title); ?>

                                                </a>
                                            </h5>

                                            <div class="vendor-name mb-2">
                                                <?php if($equipment->vendor): ?>
                                                    <?php echo e(__('By')); ?>

                                                    <a
                                                        href="<?php echo e(route('frontend.vendor.details', $equipment->vendor->username)); ?>">
                                                        <?php echo e($vendor = optional($equipment->vendor)->username); ?>

                                                    </a>
                                                <?php else: ?>
                                                    <?php echo e(__('By')); ?> <a
                                                        href="javascript:void(0)"><?php echo e(__('Admin')); ?></a>
                                                <?php endif; ?>
                                            </div>

                                            <div class="price-option">
                                                <?php if(!empty($equipment->per_day_price)): ?>
                                                    <span
                                                        class="span-btn day"><?php echo e($position == 'left' ? $symbol : ''); ?><?php echo e($equipment->per_day_price); ?><?php echo e($position == 'right' ? $symbol : ''); ?><?php echo e('/' . __('Day')); ?></span>
                                                <?php endif; ?>

                                                <?php if(!empty($equipment->per_week_price)): ?>
                                                    <span
                                                        class="span-btn active-btn week"><?php echo e($position == 'left' ? $symbol : ''); ?><?php echo e($equipment->per_week_price); ?><?php echo e($position == 'right' ? $symbol : ''); ?><?php echo e('/' . __('Week')); ?></span>
                                                <?php endif; ?>

                                                <?php if(!empty($equipment->per_month_price)): ?>
                                                    <span
                                                        class="span-btn month"><?php echo e($position == 'left' ? $symbol : ''); ?><?php echo e($equipment->per_month_price); ?><?php echo e($position == 'right' ? $symbol : ''); ?><?php echo e('/' . __('Month')); ?></span>
                                                <?php endif; ?>
                                            </div>

                                            <span class="delivary">
                                                <a href="#" class="category-search"
                                                    data-category_slug="<?php echo e($equipment->categorySlug); ?>">
                                                    <?php echo e($equipment->categoryName); ?>

                                                </a>
                                            </span>

                                            <ul class="info-list">
                                                <?php $features = explode(PHP_EOL, $equipment->features); ?>

                                                <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if($loop->iteration <= 3): ?>
                                                        <li><?php echo e($feature); ?></li>
                                                    <?php else: ?>
                                                        <li class="more-feature d-none"><?php echo e($feature); ?></li>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>

                                            <?php if(count($features) > 3): ?>
                                                <a href="#"
                                                    class="mt-2 more-feature-link"><?php echo e(__('More Features') . '...'); ?></a>
                                            <?php endif; ?>
                                        </div>

                                        <div class="pricing-bottom">
                                            <div class="d-flex flex-row justify-content-center">
                                                <div class="rate">
                                                    <div class="rating"
                                                        style="width: <?php echo e($equipment->avgRating * 20 . '%;'); ?>"></div>
                                                </div>

                                                <div class="<?php echo e($currentLanguageInfo->direction == 0 ? 'ml-3' : 'mr-3'); ?>">
                                                    <?php echo e(number_format($equipment->avgRating, 2)); ?>

                                                    (<?php echo e($equipment->ratingCount . ' ' . __('rating')); ?>)
                                                </div>
                                            </div>

                                            <a href="<?php echo e(route('equipment_details', ['slug' => $equipment->slug])); ?>"
                                                class="main-btn mt-3">
                                                <?php echo e(__('View')); ?>

                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <div class="mt-90 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Featured Equipment Section ======-->

    <!--====== Start Testimonial Section ======-->
    <?php if($secInfo->testimonial_section_status == 1): ?>
        <section class="testimonial-area light-bg pt-130 pb-130">
            <div class="container">
                <div class="row align-items-end">
                    <div class="col-lg-6">
                        <div class="section-title mb-50">
                            <span
                                class="sub-title"><?php echo e(!empty($testimonialSecInfo->subtitle) ? $testimonialSecInfo->subtitle : ''); ?></span>
                            <h2><?php echo e(!empty($testimonialSecInfo->title) ? $testimonialSecInfo->title : ''); ?></h2>
                        </div>
                    </div>
                </div>

                <?php if(count($testimonials) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Testimonial Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row testimonial-slider-one">
                        <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4">
                                <div class="testimonial-item testimonial-item-one mb-35">
                                    <div class="testimonial-content">
                                        <div class="quote">
                                            <i class="fal fa-quote-left"></i>
                                        </div>
                                        <p><?php echo e($testimonial->comment); ?></p>
                                        <h5><?php echo e($testimonial->name); ?>, <span><?php echo e($testimonial->occupation); ?></span></h5>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <div class="mt-100 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Testimonial Section ======-->

    <!--====== Start Call To Action Section ======-->
    <?php if($secInfo->call_to_action_section_status == 1): ?>
        <section class="cta-area bg-with-overlay bg-cover pt-120 pb-130 lazy"
            <?php if(!empty($callToActionSectionImage)): ?> data-bg="<?php echo e(asset('assets/img/' . $callToActionSectionImage)); ?>" <?php endif; ?>>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="cta-content-box cta-content-box-one content-white text-center">
                            <h2><?php echo e(!empty($callToActionSecInfo->subtitle) ? $callToActionSecInfo->subtitle : ''); ?></h2>
                            <h4><?php echo e(!empty($callToActionSecInfo->title) ? $callToActionSecInfo->title : ''); ?></h4>

                            <?php if(!empty($callToActionSecInfo->button_name) && !empty($callToActionSecInfo->button_url)): ?>
                                <a href="<?php echo e($callToActionSecInfo->button_url); ?>"
                                    class="main-btn"><?php echo e($callToActionSecInfo->button_name); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Call To Action Section ======-->

    <!--====== Start Blog Section ======-->
    <?php if($secInfo->blog_section_status == 1): ?>
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <div class="row align-items-end">
                    <div class="col-lg-6">
                        <div class="section-title mb-50">
                            <span
                                class="sub-title"><?php echo e(!empty($blogSecInfo->subtitle) ? $blogSecInfo->subtitle : ''); ?></span>
                            <h2><?php echo e(!empty($blogSecInfo->title) ? $blogSecInfo->title : ''); ?></h2>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="blog-arrows-one mb-60"></div>
                    </div>
                </div>

                <?php if(count($blogs) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Blog Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row blog-slider-one">
                        <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4">
                                <div class="blog-post-item blog-post-item-one">
                                    <div class="post-thumbnail">
                                        <a href="<?php echo e(route('blog_details', ['slug' => $blog->slug])); ?>" class="d-block">
                                            <img data-src="<?php echo e(asset('assets/img/blogs/' . $blog->image)); ?>"
                                                alt="image" class="lazy">
                                        </a>
                                        <a href="#" class="cat-btn post-category"
                                            data-category_slug="<?php echo e($blog->categorySlug); ?>">
                                            <?php echo e($blog->categoryName); ?>

                                        </a>
                                    </div>
                                    <div class="entry-content">
                                        <h3 class="title">
                                            <a href="<?php echo e(route('blog_details', ['slug' => $blog->slug])); ?>">
                                                <?php echo e(strlen($blog->title) > 40 ? mb_substr($blog->title, 0, 40, 'UTF-8') . '...' : $blog->title); ?>

                                            </a>
                                        </h3>
                                        <div class="post-meta">
                                            <ul>
                                                <li><span><i class="fas fa-user"></i><?php echo e($blog->author); ?></span></li>
                                                <li><span><i
                                                            class="fas fa-calendar-alt"></i><?php echo e(date_format($blog->created_at, 'M d, Y')); ?></span>
                                                </li>
                                            </ul>
                                        </div>
                                        <p>
                                            <?php echo e(strlen(strip_tags($blog->content)) > 90 ? mb_substr(strip_tags($blog->content), 0, 90, 'UTF-8') . '...' : $blog->content); ?>

                                        </p>
                                        <a href="<?php echo e(route('blog_details', ['slug' => $blog->slug])); ?>" class="btn-link">
                                            <?php echo e(__('Read More')); ?>

                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Blog Section ======-->

    <!--====== Start Partner/Sponsor Section ======-->
    <?php if($secInfo->partner_section_status == 1): ?>
        <section
            class="sponsor-area <?php if($secInfo->blog_section_status == 0): ?> pt-130 <?php endif; ?> <?php if($secInfo->subscribe_section_status == 0): ?> pb-130 <?php endif; ?>">
            <div class="container">
                <?php if(count($partners) == 0): ?>
                    <div class="row text-center">
                        <div class="col">
                            <h3><?php echo e(__('No Partner Information Found') . '!'); ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="sponsor-slider-one">
                        <?php $__currentLoopData = $partners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="sponsor-item sponsor-item-one mb-40">
                                <a href="<?php echo e($partner->url); ?>" target="_blank">
                                    <img data-src="<?php echo e(asset('assets/img/partners/' . $partner->image)); ?>"
                                        alt="sponsor image" class="lazy">
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <div class="mt-100 text-center">
                    <?php echo showAd(3); ?>

                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Partner/Sponsor Section ======-->

    <!--====== Start Newsletter Section ======-->
    <?php if($secInfo->subscribe_section_status == 1): ?>
        <section class="newsletter-area <?php if($secInfo->partner_section_status == 1): ?> pt-130 <?php endif; ?>">
            <div class="container">
                <div class="newsletter-wrapper-one">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="newsletter-content-box">
                                <div class="section-title text-center mb-30">
                                    <h2><?php echo e(__('Subscribe to Our Newsletter')); ?></h2>
                                </div>

                                <form class="newsletter-form subscription-form" action="<?php echo e(route('store_subscriber')); ?>"
                                    method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="form_group">
                                        <input type="email" class="form_control"
                                            placeholder="<?php echo e(__('Enter Your Email Address')); ?>" name="email_id">
                                        <button type="submit" class="newsletter-btn"><?php echo e(__('Subscribe')); ?></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!--====== End Newsletter Section ======-->

    
    <form class="d-none" action="<?php echo e(route('all_equipment')); ?>" method="GET">
        <input type="hidden" id="category-id" name="category">

        <button type="submit" id="submitBtn"></button>
    </form>
    

    
    <form class="d-none" action="<?php echo e(route('blog')); ?>" method="GET">
        <input type="hidden" id="categoryKey" name="category">

        <button type="submit" id="form-submit-btn"></button>
    </form>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script type="text/javascript" src="<?php echo e(asset('assets/js/equipment.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/home/<USER>/ ?>