/* -----------------------------------------
	Hero Banner 2
----------------------------------------- */
.hero-banner.hero-banner_v2 {
  padding-bottom: 100px;
}
.hero-banner.hero-banner_v2 .banner-content {
  padding-top: 260px;
}
.hero-banner.hero-banner_v2 .banner-content .subtitle .line {
  display: inline-block;
  width: 40px;
  height: 4px;
  background-color: var(--color-primary);
  margin-inline-end: 5px;
  -webkit-clip-path: polygon(0 0, 100% 0%, 85% 100%, 0% 100%);
          clip-path: polygon(0 0, 100% 0%, 85% 100%, 0% 100%);
}
.hero-banner.hero-banner_v2 .form-wrapper {
  background-color: var(--bg-white);
  z-index: 3;
}
.hero-banner.hero-banner_v2 .right-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-inline-start: auto;
  height: 100%;
  width: 50%;
}
.hero-banner.hero-banner_v2 .right-content .overlap {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-inline-start: auto;
  width: 80%;
  height: 100%;
  z-index: -1;
}
.hero-banner.hero-banner_v2 .right-content .overlap::after {
  position: absolute;
  content: "";
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-dark-rgb), 1);
  opacity: 0.3;
  z-index: -1;
}
.hero-banner.hero-banner_v2 .right-content .swiper-slide {
  position: relative;
  overflow: hidden;
  padding-top: 200px;
}
.hero-banner.hero-banner_v2 .swiper-pagination .swiper-pagination-bullet {
  background-color: var(--color-white);
}