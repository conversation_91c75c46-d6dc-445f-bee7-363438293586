<?php

namespace App\Http\Controllers\FrontEnd\Instrument;

use DateTime;
use App\Models\Commission;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Http\Helpers\BasicMailer;
use App\Models\BasicSettings\Basic;
use App\Models\Instrument\Location;
use App\Http\Controllers\Controller;
use App\Models\Instrument\Equipment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\BasicSettings\MailTemplate;
use App\Models\Instrument\EquipmentBooking;
use App\Http\Requests\Instrument\BookingProcessRequest;
use App\Http\Controllers\FrontEnd\MiscellaneousController;
use App\Http\Controllers\FrontEnd\PaymentGateway\YocoController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PaytmController;
use App\Http\Controllers\FrontEnd\PaymentGateway\MollieController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PayPalController;
use App\Http\Controllers\FrontEnd\PaymentGateway\StripeController;
use App\Http\Controllers\FrontEnd\PaymentGateway\XenditController;
use App\Http\Controllers\FrontEnd\PaymentGateway\IyzipayController;
use App\Http\Controllers\FrontEnd\PaymentGateway\OfflineController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PaytabsController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PhonePeController;
use App\Http\Controllers\FrontEnd\PaymentGateway\MidtransController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PaystackController;
use App\Http\Controllers\FrontEnd\PaymentGateway\RazorpayController;
use App\Http\Controllers\FrontEnd\PaymentGateway\InstamojoController;
use App\Http\Controllers\FrontEnd\PaymentGateway\ToyyibpayController;
use App\Http\Controllers\FrontEnd\PaymentGateway\MyfatoorahController;
use App\Http\Controllers\FrontEnd\PaymentGateway\FlutterwaveController;
use App\Http\Controllers\FrontEnd\PaymentGateway\MercadoPagoController;
use App\Http\Controllers\FrontEnd\PaymentGateway\PerfectMoneyController;

class BookingProcessController extends Controller
{
    public function index(BookingProcessRequest $request)
    {
        if (!$request->exists('gateway')) {
            Session::flash('error', 'Please select a payment method.');
            return redirect()->back()->withInput();
        } elseif ($request['gateway'] == 'paypal') {
            $paypal = new PayPalController();

            return $paypal->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'instamojo') {
            $instamojo = new InstamojoController();

            return $instamojo->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'paystack') {
            $paystack = new PaystackController();

            return $paystack->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'flutterwave') {
            $flutterwave = new FlutterwaveController();

            return $flutterwave->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'razorpay') {
            $razorpay = new RazorpayController();

            return $razorpay->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'mercadopago') {
            $mercadopago = new MercadoPagoController();

            return $mercadopago->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'mollie') {
            $mollie = new MollieController();

            return $mollie->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'stripe') {
            $stripe = new StripeController();

            return $stripe->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'paytm') {
            $paytm = new PaytmController();

            return $paytm->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'midtrans') {
            $midtrans = new MidtransController();

            return $midtrans->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'paytabs') {
            $paytabs = new PaytabsController();

            return $paytabs->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'toyyibpay') {
            $toyyibpay = new ToyyibpayController();

            return $toyyibpay->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'phonepe') {
            $PhonePe = new PhonePeController();

            return $PhonePe->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'myfatoorah') {
            $myfatoorah = new MyfatoorahController();

            return $myfatoorah->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'xendit') {
            $xendit = new XenditController();

            return $xendit->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'yoco') {
            $yoco = new YocoController();

            return $yoco->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'iyzico') {
            $izyico = new IyzipayController();

            return $izyico->index($request, 'equipment booking');
        } elseif ($request['gateway'] == 'perfect_money') {
            $yoco = new PerfectMoneyController();

            return $yoco->index($request, 'product purchase');
        } else {
            $offline = new OfflineController();

            return $offline->index($request, 'equipment booking');
        }
    }

    public function calculation(Request $request)
    {
        if ($request->session()->has('totalPrice')) {
            $total = $request->session()->get('totalPrice');
        }

        if ($request->session()->has('equipmentDiscount')) {
            $discountVal = $request->session()->get('equipmentDiscount');
        }

        $discount = isset($discountVal) ? floatval($discountVal) : 0.0;
        $subtotal = $total - $discount;

        $taxData = Basic::select('equipment_tax_amount')->first();
        $taxAmount = floatval($taxData->equipment_tax_amount);
        $calculatedTax = $subtotal * ($taxAmount / 100);

        $shippingCharge = 0.0;

        if ($request['shipping_method'] == 'two way delivery') {
            $locationId = $request['location'];

            $location = Location::query()->find($locationId);
            $shippingCharge = floatval($location->charge);
        }

        //get security deposit amount
        $equipment = Equipment::where('id', $request['equipment_id'])->first();

        $grandTotal = $subtotal + $calculatedTax + $shippingCharge + $equipment->security_deposit_amount;

        $calculatedData = [
            'total' => $total,
            'discount' => $discount,
            'subtotal' => $subtotal,
            'shippingCharge' => $request['shipping_method'] == 'two way delivery' ? $shippingCharge : null,
            'tax' => $calculatedTax,
            'grandTotal' => $grandTotal,
            'security_deposit_amount' => $equipment->security_deposit_amount,
        ];

        return $calculatedData;
    }

    public function getDates($dateString)
    {
        $arrOfDate = explode(' ', $dateString);
        $date_1 = $arrOfDate[0];
        $date_2 = $arrOfDate[2];

        $dates = [
            'startDate' => date_create($date_1),
            'endDate' => date_create($date_2),
        ];

        return $dates;
    }

    public function getLocation($locationId)
    {
        $location = Location::query()->find($locationId);
        $locationName = $location->name;

        return $locationName;
    }

    public function storeData($arrData)
    {
        $equipment = Equipment::findOrFail($arrData['equipmentId']);
        if (!empty($equipment)) {
            if ($equipment->vendor_id != null) {
                $vendor_id = $equipment->vendor_id;
            } else {
                $vendor_id = null;
            }
        } else {
            $vendor_id = null;
        }
        //generate 8 digit booking number
        $pool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $booking_number = substr(str_shuffle(str_repeat($pool, 5)), 0, 8);

        $commission = Commission::first();
        $bookingInfo = EquipmentBooking::query()->create([
            'user_id' => Auth::guard('web')->check() == true ? Auth::guard('web')->user()->id : null,
            'booking_number' => $booking_number,
            'name' => $arrData['name'],
            'contact_number' => $arrData['contactNumber'],
            'email' => $arrData['email'],
            'vendor_id' => $vendor_id,
            'equipment_id' => $arrData['equipmentId'],
            'start_date' => $arrData['startDate'],
            'end_date' => $arrData['endDate'],
            'shipping_method' => $arrData['shippingMethod'],
            'location' => $arrData['location'],
            'total' => array_key_exists('total', $arrData) ? $arrData['total'] : null,
            'discount' => array_key_exists('discount', $arrData) ? $arrData['discount'] : null,
            'shipping_cost' => array_key_exists('shippingCost', $arrData) ? $arrData['shippingCost'] : null,
            'tax' => array_key_exists('tax', $arrData) ? $arrData['tax'] : null,
            'grand_total' => array_key_exists('grandTotal', $arrData) ? $arrData['grandTotal'] : null,
            'security_deposit_amount' => array_key_exists('security_deposit_amount', $arrData) ? $arrData['security_deposit_amount'] : null,

            'currency_symbol' => array_key_exists('currencySymbol', $arrData) ? $arrData['currencySymbol'] : null,
            'currency_symbol_position' => array_key_exists('currencySymbolPosition', $arrData) ? $arrData['currencySymbolPosition'] : null,
            'currency_text' => array_key_exists('currencyText', $arrData) ? $arrData['currencyText'] : null,
            'currency_text_position' => array_key_exists('currencyTextPosition', $arrData) ? $arrData['currencyTextPosition'] : null,
            'booking_type' => array_key_exists('bookingType', $arrData) ? $arrData['bookingType'] : null,
            'price_message' => array_key_exists('priceMessage', $arrData) ? $arrData['priceMessage'] : null,
            'payment_method' => array_key_exists('paymentMethod', $arrData) ? $arrData['paymentMethod'] : null,
            'gateway_type' => array_key_exists('gatewayType', $arrData) ? $arrData['gatewayType'] : null,
            'payment_status' => $arrData['paymentStatus'],
            'shipping_status' => $arrData['shippingStatus'],
            'attachment' => array_key_exists('attachment', $arrData) ? $arrData['attachment'] : null,
            'commission_percentage' => $commission->equipment_commission,
            'conversation_id' => array_key_exists('conversation_id', $arrData) ? $arrData['conversation_id'] : null,
        ]);

        return $bookingInfo;
    }

    public function generateInvoice($bookingInfo)
    {
        $fileName = $bookingInfo->booking_number . '.pdf';

        $data['bookingInfo'] = $bookingInfo;

        $directory = config('dompdf.public_path') . 'equipment/';
        @mkdir($directory, 0775, true);

        $fileLocated = $directory . $fileName;

        $data['taxData'] = Basic::select('equipment_tax_amount')->first();

        Pdf::loadView('frontend.equipment.invoice', $data)->save($fileLocated);

        return $fileName;
    }

    public function prepareMail($bookingInfo, $transaction_id)
    {
        // get the mail template info from db
        $mailTemplate = MailTemplate::query()->where('mail_type', '=', 'equipment_booking')->first();
        $mailData['subject'] = $mailTemplate->mail_subject;
        $mailBody = $mailTemplate->mail_body;

        // get the website title info from db
        $info = Basic::select('website_title')->first();

        // preparing dynamic data
        $customerName = $bookingInfo->name;
        $bookingNumber = $bookingInfo->booking_id;
        $bookingDate = date_format($bookingInfo->created_at, 'M d, Y');

        $equipmentId = $bookingInfo->equipment_id;
        $equipment = Equipment::query()->find($equipmentId);

        $vendor = $equipment->vendor()->first();

        $misc = new MiscellaneousController();
        $language = $misc->getLanguage();

        $equipmentInfo = $equipment
            ->content()
            ->where('language_id', $language->id)
            ->first();

        $equipmentTitle = $equipmentInfo->title;

        $startDate = $bookingInfo->start_date;

        if (!$startDate instanceof DateTime) {
            $startDate = new DateTime($startDate);
        }
        $startDate = $startDate->format('M d, Y');
        // $startDate = new DateTime($bookingInfo->start_date);

        

        $endDate = $bookingInfo->end_date;
        if (!$endDate instanceof DateTime) {
            $endDate = new DateTime($endDate);
        }
        $endDate = $endDate->format('M d, Y');

        // $endDate = new DateTime($bookingInfo->start_date);
        // $endDate = $endDate->format('M d, Y');

        // $startDate = date_format($bookingInfo->start_date, 'M d, Y');
        // $endDate = date_format($bookingInfo->end_date, 'M d, Y');
        $websiteTitle = $info->website_title;

        if (Auth::guard('web')->check() == true) {
            $bookingLink = '<p>Booking Details: <a href=' . url('user/equipment-booking/' . $bookingInfo->id . '/details') . '>Click Here</a></p>';
        } else {
            $bookingLink = '';
        }
        if ($vendor != null) {
            $vendor_details_link = '<p>Vendor Details: <a href=' . url('vendors/' . $vendor->username) . '>Click Here</a></p>';
        } else {
            $vendor_details_link = '';
        }

        // replacing with actual data
        $mailBody = str_replace('{transaction_id}', $transaction_id, $mailBody);
        $mailBody = str_replace('{customer_name}', $customerName, $mailBody);
        $mailBody = str_replace('{booking_number}', $bookingNumber, $mailBody);
        $mailBody = str_replace('{booking_date}', $bookingDate, $mailBody);
        $mailBody = str_replace('{equipment_name}', $equipmentTitle, $mailBody);
        $mailBody = str_replace('{start_date}', $startDate, $mailBody);
        $mailBody = str_replace('{end_date}', $endDate, $mailBody);
        $mailBody = str_replace('{website_title}', $websiteTitle, $mailBody);
        $mailBody = str_replace('{booking_link}', $bookingLink, $mailBody);
        $mailBody = str_replace('{vendor_details_link}', $vendor_details_link, $mailBody);

        $mailData['body'] = $mailBody;

        $mailData['recipient'] = $bookingInfo->email;

        $mailData['invoice'] = public_path('assets/file/invoices/equipment/') . $bookingInfo->invoice;

        BasicMailer::sendMail($mailData);

        return;
    }

    public function complete($type = null)
    {
        $misc = new MiscellaneousController();

        $queryResult['bgImg'] = $misc->getBreadcrumb();

        $queryResult['bookingType'] = $type;

        if (session()->has('shippingMethod')) {
            session()->forget('shippingMethod');
        }

        return view('frontend.payment.booking-success', $queryResult);
    }

    public function cancel(Request $request)
    {
        Session::flash('error', 'Sorry, an error has occured!');

        return redirect()->route('all_equipment');
    }
}
