<div class="header-navigation">
  <div class="container-fluid pl-0 pr-0">
    <div class="primary-menu d-flex align-items-center justify-content-between">
      <div class="site-branding">
        <?php if(!empty($websiteInfo->logo)): ?>
          <a href="<?php echo e(route('index')); ?>" class="brand-logo">
            <img data-src="<?php echo e(asset('assets/img/' . $websiteInfo->logo)); ?>" alt="website logo" class="lazy">
          </a>
        <?php endif; ?>
      </div>

      <div class="nav-menu">
        <div class="navbar-close"><i class="fal fa-times"></i></div>

        <nav class="main-menu">
          <ul>
            <?php $menuDatas = json_decode($menuInfos); ?>
            <?php $__currentLoopData = $menuDatas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <?php $href = get_href($menuData); ?>

              <?php if(!property_exists($menuData, 'children')): ?>
                <li class="menu-item">
                  <a href="<?php echo e($href); ?>"><?php echo e($menuData->text); ?></a>
                </li>
              <?php else: ?>
                <li class="menu-item menu-item-has-children">
                  <a href="<?php echo e($href); ?>"><?php echo e($menuData->text); ?></a>
                  <ul class="sub-menu">
                    <?php $childMenuDatas = $menuData->children; ?>

                    <?php $__currentLoopData = $childMenuDatas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childMenuData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <?php $child_href = get_href($childMenuData); ?>

                      <li><a href="<?php echo e($child_href); ?>"><?php echo e($childMenuData->text); ?></a></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </ul>
                </li>
              <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </ul>
        </nav>
      </div>

      <div class="navbar-toggler">
        <span></span><span></span><span></span>
      </div>

      <div class="header-right-nav">
        <?php if(count($socialMediaInfos) > 0): ?>
          <div class="social-box">
            <ul class="social-link">
              <?php $__currentLoopData = $socialMediaInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $socialMediaInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                  <a href="<?php echo e($socialMediaInfo->url); ?>" target="_blank">
                    <i class="<?php echo e($socialMediaInfo->icon); ?>"></i>
                  </a>
                </li>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/partials/header/header-nav-v1.blade.php ENDPATH**/ ?>