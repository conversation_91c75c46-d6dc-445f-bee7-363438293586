<?php $__env->startSection('content'); ?>
  <div class="mt-2 mb-4">
    <h2 class="pb-2"><?php echo e(__('Welcome back,')); ?> <?php echo e($authAdmin->first_name . ' ' . $authAdmin->last_name . '!'); ?></h2>
  </div>

  
  <?php
    if (!is_null($roleInfo)) {
        $rolePermissions = json_decode($roleInfo->permissions);
    }
  ?>

  <div class="row dashboard-items">
    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Total Revenue', $rolePermissions))): ?>
      <div class="col-md-3">
        <a href="<?php echo e(route('admin.monthly_earning')); ?>">
          <div class="card card-stats card-secondary card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Lifetime Earnings')); ?></p>
                    <h4 class="card-title">
                      <?php echo e($settings->base_currency_symbol_position == 'left' ? $settings->base_currency_symbol : ''); ?>

                      <?php echo e($earning->total_revenue); ?>

                      <?php echo e($settings->base_currency_symbol_position == 'right' ? $settings->base_currency_symbol : ''); ?>

                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>
    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Total Earning', $rolePermissions))): ?>
      <div class="col-md-3">
        <a href="<?php echo e(route('admin.monthly_profit')); ?>">
          <div class="card card-stats card-info card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Total Profit')); ?></p>
                    <h4 class="card-title">
                      <?php echo e($settings->base_currency_symbol_position == 'left' ? $settings->base_currency_symbol : ''); ?>

                      <?php echo e($earning->total_earning); ?>

                      <?php echo e($settings->base_currency_symbol_position == 'right' ? $settings->base_currency_symbol : ''); ?>

                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Total Earning', $rolePermissions))): ?>
      <div class="col-md-3">
        <a href="<?php echo e(route('admin.transcation')); ?>">
          <div class="card card-stats card-primary card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-exchange-alt"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Total Transcation')); ?></p>
                    <h4 class="card-title"><?php echo e($transcation_count); ?>

                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>
    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Equipment', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.equipment_management.all_equipment', ['language' => $defaultLang->code])); ?>">
          <div class="card card-stats card-success card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-truck-container"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Equipment')); ?></p>
                    <h4 class="card-title"><?php echo e($totalEquipment); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Equipment Booking', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.equipment_booking.bookings', ['language' => $defaultLang->code])); ?>">
          <div class="card card-stats card-danger card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-calendar-alt"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Bookings')); ?></p>
                    <h4 class="card-title"><?php echo e($totalBooking); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Shop Management', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.shop_management.products', ['language' => $defaultLang->code])); ?>">
          <div class="card card-stats card-success card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-box-alt"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Products')); ?></p>
                    <h4 class="card-title"><?php echo e($totalProduct); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Shop Management', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.shop_management.orders')); ?>">
          <div class="card card-stats card-warning card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-shopping-cart"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Orders')); ?></p>
                    <h4 class="card-title"><?php echo e($totalOrder); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Blog Management', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.blog_management.blogs', ['language' => $defaultLang->code])); ?>">
          <div class="card card-stats card-info card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-blog"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Blog')); ?></p>
                    <h4 class="card-title"><?php echo e($totalBlog); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('User Management', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.user_management.registered_users')); ?>">
          <div class="card card-stats card-orchid card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="la flaticon-users"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Users')); ?></p>
                    <h4 class="card-title"><?php echo e($totalUser); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('User Management', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.user_management.subscribers')); ?>">
          <div class="card card-stats card-dark card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-bell"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Subscribers')); ?></p>
                    <h4 class="card-title"><?php echo e($totalSubscriber); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>

    <?php if(is_null($roleInfo) || (!empty($rolePermissions) && in_array('Partners', $rolePermissions))): ?>
      <div class="col-sm-6 col-md-3">
        <a href="<?php echo e(route('admin.home_page.partners')); ?>">
          <div class="card card-stats card-secondary card-round">
            <div class="card-body">
              <div class="row">
                <div class="col-5">
                  <div class="icon-big text-center">
                    <i class="fal fa-handshake"></i>
                  </div>
                </div>

                <div class="col-7 col-stats">
                  <div class="numbers">
                    <p class="card-category"><?php echo e(__('Partners')); ?></p>
                    <h4 class="card-title"><?php echo e($totalPartner); ?></h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a>
      </div>
    <?php endif; ?>
  </div>

  <div class="row">
    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <div class="card-title"><?php echo e(__('Number of Equipment Bookings')); ?> (<?php echo e(date('Y')); ?>)</div>
        </div>

        <div class="card-body">
          <div class="chart-container">
            <canvas id="bookingChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <div class="card-title"><?php echo e(__('Income from Equipment Bookings')); ?> (<?php echo e(date('Y')); ?>)</div>
        </div>

        <div class="card-body">
          <div class="chart-container">
            <canvas id="incomeChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
  
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
  
  <script type="text/javascript" src="<?php echo e(asset('assets/js/chart.min.js')); ?>"></script>

  <script>
    'use strict';
    const monthArr = <?php echo json_encode($months); ?>;
    const bookingArr = <?php echo json_encode($bookings); ?>;
    const incomeArr = <?php echo json_encode($incomes); ?>;
  </script>

  <script type="text/javascript" src="<?php echo e(asset('assets/js/chart-init.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/backend/admin/dashboard.blade.php ENDPATH**/ ?>