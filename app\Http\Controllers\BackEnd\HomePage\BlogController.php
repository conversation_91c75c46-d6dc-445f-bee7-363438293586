<?php

namespace App\Http\Controllers\BackEnd\HomePage;

use App\Models\Language;
use Illuminate\Http\Request;
use App\Http\Helpers\UploadFile;
use App\Http\Controllers\Controller;
use App\Models\HomePage\BlogSection;
use Illuminate\Support\Facades\Session;

class Blog<PERSON>ontroller extends Controller
{
    public function index(Request $request)
    {
        $language = Language::query()
            ->where('code', '=', $request->language)
            ->first();
        $information['language'] = $language;

        $information['data'] = $language->blogSection()->first();

        $information['langs'] = Language::all();

        return view('backend.home-page.blog-section', $information);
    }

    public function update(Request $request)
    {
        $language = Language::query()
            ->where('code', '=', $request->language)
            ->first();

        $blog_section = $language->blogSection()->first();

        if ($request->hasFile('image')) {
            $imageName = UploadFile::store(public_path('assets/img/blog-images/'), $request->file('image'));
        }

        if (empty($blog_section)) {
            BlogSection::query()->create(
              $request->except('language_id', 'subtitle', 'title', 'blog_section_image') +[
                'language_id' => $language->id, 
                'subtitle' => $request->subtitle, 
                'title' => $request->title, 
                'blog_section_image' => $imageName ?? null
              ]
            );
        } else {
          $blog_section->update(
            $request->except('language_id', 'subtitle', 'title', 'blog_section_image') +[
              'language_id' => $language->id, 
              'subtitle' => $request->subtitle, 
              'title' => $request->title, 
              'blog_section_image' => $request->hasFile('image') ? $imageName : $blog_section->blog_section_image
            ]
          );
        }

        Session::flash('success', 'Blog section updated successfully!');

        return redirect()->back();
    }
}
