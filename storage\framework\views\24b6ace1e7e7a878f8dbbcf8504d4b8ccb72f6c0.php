<!DOCTYPE html>
<html lang="<?php echo e($currentLanguageInfo->code); ?>" <?php if($currentLanguageInfo->direction == 1): ?> dir="rtl" <?php endif; ?>>

<head>
    
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('assets/img/' . $websiteInfo->favicon)); ?>">
    
 <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    
    <title><?php echo $__env->yieldContent('pageHeading'); ?> <?php echo e('| ' . $websiteInfo->website_title); ?></title>

    <meta name="keywords" content="<?php echo $__env->yieldContent('metaKeywords'); ?>">
    <meta name="description" content="<?php echo $__env->yieldContent('metaDescription'); ?>">
    <meta name="theme-color" content="<?php echo e($basicInfo->primary_color); ?>" />

    
    <?php
        $primaryColor = $basicInfo->primary_color;
        $secondaryColor = $basicInfo->secondary_color;
        $breadcrumbOverlayColor = $basicInfo->breadcrumb_overlay_color;
    ?>


    <?php if($basicInfo->theme_version == 1 || $basicInfo->theme_version == 2): ?>
        
        <?php if ($__env->exists('frontend.partials.styles')) echo $__env->make('frontend.partials.styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        
    <?php elseif(
        $basicInfo->theme_version == 3 ||
            $basicInfo->theme_version == 4 ||
            $basicInfo->theme_version == 5 ||
            $basicInfo->theme_version == 6): ?>
        <?php if(request()->routeIs('index')): ?>
            <?php if ($__env->exists('frontend.partials.theme3456_styles')) echo $__env->make('frontend.partials.theme3456_styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php else: ?>
            <?php if ($__env->exists('frontend.partials.styles')) echo $__env->make('frontend.partials.styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        


        <style>
            :root {
                --color-primary: #<?php echo e($primaryColor); ?>;
                --color-secondary: #<?php echo e($secondaryColor); ?>;
            }
        </style>
    <?php else: ?>
    <?php endif; ?>
    <link rel="manifest" crossorigin="use-credentials" href="<?php echo e(asset('manifest.json')); ?>" />

    
    <?php echo $__env->yieldContent('style'); ?>
    <style>
        .whatsapp-btn {
            z-index: 3;
        }
    </style>
</head>

<body class="<?php if($basicInfo->theme_version == 4): ?> theme-dark <?php endif; ?>
  <?php if(!request()->routeIs('index') && ($basicInfo->theme_version == 3 ||
          $basicInfo->theme_version == 4 ||
          $basicInfo->theme_version == 5)): ?> breadcrumbs-area_v2_active <?php endif; ?>">

    
    <div class="preloader">
        <div class="lds-ellipsis">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
    

    <?php if(
        $basicInfo->theme_version == 3 ||
            $basicInfo->theme_version == 4 ||
            $basicInfo->theme_version == 5 ||
            $basicInfo->theme_version == 6): ?>
        <div class="cart-wrapper">
            <a href="<?php echo e(route('shop.cart')); ?>" class="btn-icon">
                <i class="fal fa-shopping-bag"></i>
                <span id="product-count"><?php echo e(count($cartItemInfo)); ?> <?php echo e(__('Items')); ?></span>
            </a>
        </div>
    <?php endif; ?>

    
    <?php if($basicInfo->theme_version == 1): ?>
        <header class="header-area-one">
            
            <?php if ($__env->exists('frontend.partials.header.header-top-v1')) echo $__env->make('frontend.partials.header.header-top-v1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
            <?php if ($__env->exists('frontend.partials.header.header-nav-v1')) echo $__env->make('frontend.partials.header.header-nav-v1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </header>
    <?php elseif($basicInfo->theme_version == 2): ?>
        <header class="header-area-two">
            
            <?php if ($__env->exists('frontend.partials.header.header-top-v2')) echo $__env->make('frontend.partials.header.header-top-v2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
            <?php if ($__env->exists('frontend.partials.header.header-nav-v2')) echo $__env->make('frontend.partials.header.header-nav-v2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </header>
    <?php elseif($basicInfo->theme_version == 3): ?>
        <?php if ($__env->exists('frontend.partials.header.header-nav-v3')) echo $__env->make('frontend.partials.header.header-nav-v3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php elseif($basicInfo->theme_version == 4): ?>
        <?php if ($__env->exists('frontend.partials.header.header-nav-v4')) echo $__env->make('frontend.partials.header.header-nav-v4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php elseif($basicInfo->theme_version == 5): ?>
        <?php if ($__env->exists('frontend.partials.header.header-nav-v5')) echo $__env->make('frontend.partials.header.header-nav-v5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php elseif($basicInfo->theme_version == 6): ?>
        <?php if ($__env->exists('frontend.partials.header.header-nav-v6')) echo $__env->make('frontend.partials.header.header-nav-v6', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    

    <?php echo $__env->yieldContent('content'); ?>

    
    <?php if($basicInfo->theme_version == 1 || $basicInfo->theme_version == 2): ?>
        <a href="#" class="back-to-top"><i class="fas fa-angle-up"></i></a>
    <?php else: ?>
        <div class="go-top"><i class="fal fa-angle-up"></i></div>
    <?php endif; ?>
    

    
    <?php if($basicInfo->whatsapp_status == 1 && $basicInfo->tawkto_status == 0): ?>
        <div class="whatsapp-btn"></div>
    <?php endif; ?>
    
    <?php if($basicInfo->whatsapp_status == 0 && $basicInfo->tawkto_status == 1): ?>
        <?php
            $directLink = str_replace('tawk.to', 'embed.tawk.to', $basicInfo->tawkto_direct_chat_link);
            $directLink = str_replace('chat/', '', $directLink);
        ?>
        <!--Start of Tawk.to Script-->
        <script type="text/javascript">
            "use strict";
            var Tawk_API = Tawk_API || {},
                Tawk_LoadStart = new Date();
            (function() {
                var s1 = document.createElement("script"),
                    s0 = document.getElementsByTagName("script")[0];
                s1.async = true;
                s1.src = '<?php echo e($directLink); ?>';
                s1.charset = 'UTF-8';
                s1.setAttribute('crossorigin', '*');
                s0.parentNode.insertBefore(s1, s0);
            })();
        </script>
        <!--End of Tawk.to Script-->
    <?php endif; ?>

    
    <?php if ($__env->exists('frontend.partials.popups')) echo $__env->make('frontend.partials.popups', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    
    <?php if(!is_null($cookieAlertInfo) && $cookieAlertInfo->cookie_alert_status == 1): ?>
        <?php echo $__env->make('cookie-consent::index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>


    

    <?php if ($__env->exists('frontend.partials.footer.footer-v' . $basicInfo->theme_version)) echo $__env->make('frontend.partials.footer.footer-v' . $basicInfo->theme_version, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if($basicInfo->theme_version == 1 || $basicInfo->theme_version == 2): ?>
        <?php if ($__env->exists('frontend.partials.scripts')) echo $__env->make('frontend.partials.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php elseif(
        $basicInfo->theme_version == 3 ||
            $basicInfo->theme_version == 4 ||
            $basicInfo->theme_version == 5 ||
            $basicInfo->theme_version == 6): ?>
        <?php if(request()->routeIs('index')): ?>
            <?php if ($__env->exists('frontend.partials.theme3456_scripts')) echo $__env->make('frontend.partials.theme3456_scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php else: ?>
            <?php if ($__env->exists('frontend.partials.scripts')) echo $__env->make('frontend.partials.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>
    <?php else: ?>
    <?php endif; ?>
    

    
    <?php echo $__env->yieldContent('script'); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/layout.blade.php ENDPATH**/ ?>