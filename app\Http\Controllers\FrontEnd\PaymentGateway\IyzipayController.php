<?php

namespace App\Http\Controllers\FrontEnd\PaymentGateway;

use Config\Iyzipay;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\FrontEnd\Shop\PurchaseProcessController;
use App\Http\Controllers\FrontEnd\Instrument\BookingProcessController;

class IyzipayController extends Controller
{
    public function index(Request $request, $paymentFor)
    {
        // card validation start
        if ($paymentFor == 'product purchase') {
            $rules = [
                'identity_number' => 'required',
                'zipcode' => 'required',
            ];
        } else {
            $rules = [
                'identity_number' => 'required',
                'city' => 'required',
                'country' => 'required',
                'address' => 'required',    
                'zipcode' => 'required',
            ];
        }
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        // card validation end
        $currencyInfo = $this->getCurrencyInfo();
        if ($currencyInfo->base_currency_text != 'TRY') {
            return redirect()->back()->with('warning', 'Invalid currency for IyziPay payment.');
        }
        if ($paymentFor == 'product purchase') {
            // get the products from session
            if ($request->session()->has('productCart')) {
                $productList = $request->session()->get('productCart');
            } else {
                Session::flash('error', 'Something went wrong!');
                return redirect()->route('shop.products');
            }
            $purchaseProcess = new PurchaseProcessController();
            // do calculation
            $calculatedData = $purchaseProcess->calculation($request, $productList);
        } elseif ($paymentFor == 'equipment booking') {
            // check whether the equipment lowest price exist or not in session
            if (!$request->session()->has('totalPrice')) {
                Session::flash('error', 'Something went wrong!');
                return redirect()->route('all_equipment');
            }
            $bookingProcess = new BookingProcessController();
            // do calculation
            $calculatedData = $bookingProcess->calculation($request);
        }

        if ($paymentFor == 'product purchase') {
            $arrData = [
                'billingFirstName' => $request['billing_first_name'],
                'billingLastName' => $request['billing_last_name'],
                'billingEmail' => $request['billing_email'],
                'billingContactNumber' => $request['billing_contact_number'],
                'billingAddress' => $request['billing_address'],
                'billingCity' => $request['billing_city'],
                'billingState' => $request['billing_state'],
                'billingCountry' => $request['billing_country'],
                'shippingFirstName' => $request['shipping_first_name'],
                'shippingLastName' => $request['shipping_last_name'],
                'shippingEmail' => $request['shipping_email'],
                'shippingContactNumber' => $request['shipping_contact_number'],
                'shippingAddress' => $request['shipping_address'],
                'shippingCity' => $request['shipping_city'],
                'shippingState' => $request['shipping_state'],
                'shippingCountry' => $request['shipping_country'],
                'total' => $calculatedData['total'],
                'discount' => $calculatedData['discount'],
                'productShippingChargeId' => $request->exists('charge_id') ? $request['charge_id'] : null,
                'shippingCharge' => $calculatedData['shippingCharge'],
                'tax' => $calculatedData['tax'],
                'grandTotal' => $calculatedData['grandTotal'],
                'currencyText' => $currencyInfo->base_currency_text,
                'currencyTextPosition' => $currencyInfo->base_currency_text_position,
                'currencySymbol' => $currencyInfo->base_currency_symbol,
                'currencySymbolPosition' => $currencyInfo->base_currency_symbol_position,
                'paymentMethod' => 'Iyzico',
                'gatewayType' => 'online',
                'paymentStatus' => 'pending',
                'orderStatus' => 'pending',
            ];

            $title = 'Purchase Product';
            $notifyURL = route('shop.iyzico.notify');
        } elseif ($paymentFor == 'equipment booking') {
            // get start & end date
            $dates = $bookingProcess->getDates($request['dates']);
            // get location name
            $location_name = $bookingProcess->getLocation($request['location']);

            $arrData = [
                'name' => $request['name'],
                'contactNumber' => $request['contact_number'],
                'email' => $request['email'],
                'equipmentId' => $request['equipment_id'],
                'startDate' => $dates['startDate'],
                'endDate' => $dates['endDate'],
                'shippingMethod' => $request->filled('shipping_method') ? $request['shipping_method'] : null,
                'location' => $location_name,
                'total' => $calculatedData['total'],
                'discount' => $calculatedData['discount'],
                'shippingCost' => $calculatedData['shippingCharge'],
                'tax' => $calculatedData['tax'],
                'grandTotal' => $calculatedData['grandTotal'],
                'security_deposit_amount' => $calculatedData['security_deposit_amount'],
                'currencySymbol' => $currencyInfo->base_currency_symbol,
                'currencySymbolPosition' => $currencyInfo->base_currency_symbol_position,
                'currencyText' => $currencyInfo->base_currency_text,
                'currencyTextPosition' => $currencyInfo->base_currency_text_position,
                'paymentMethod' => 'Iyzico',
                'gatewayType' => 'online',
                'paymentStatus' => 'pending',
                'shippingStatus' => !$request->filled('shipping_method') ? null : 'pending',
            ];

            $title = 'Equipment Booking';
            $notifyURL = route('equipment.iyzico.notify');
        }

        // Payment start
        $options = Iyzipay::options();
        $conversion_id = uniqid(9999, 999999);
        $basket_id = 'B' . uniqid(999, 99999);
        # create request class
        $req = new \Iyzipay\Request\CreatePayWithIyzicoInitializeRequest();
        $req->setLocale(\Iyzipay\Model\Locale::EN);
        $req->setConversationId($conversion_id);
        $req->setPrice($calculatedData['grandTotal']);
        $req->setPaidPrice($calculatedData['grandTotal']);
        $req->setCurrency(\Iyzipay\Model\Currency::TL);
        $req->setBasketId($basket_id);
        $req->setPaymentGroup(\Iyzipay\Model\PaymentGroup::PRODUCT);
        $req->setCallbackUrl($notifyURL);
        $req->setEnabledInstallments([2, 3, 6, 9]);
        $name = $request['billing_first_name'] ?? $request['name'];
        $email = $request['billing_email'] ?? $request['email'];
        $address = $request['billing_address'] ?? $request['address'];
        $city = $request['billing_city'] ?? $request['city'];
        $country = $request['billing_country'] ?? $request['country'];
        $state = $request['billing_state'] ?? $request['state'];
        $zipcode = $request['zipcode'];
        $phone = $request['billing_contact_number'] ?? $request['contact_number'];
        $identity_number = $request['identity_number'];

        $buyer = new \Iyzipay\Model\Buyer();
        $buyer->setId(uniqid());
        $buyer->setName($name);
        $buyer->setSurname($name);
        $buyer->setGsmNumber($phone);
        $buyer->setEmail($email);
        $buyer->setIdentityNumber($identity_number);
        $buyer->setLastLoginDate('');
        $buyer->setRegistrationDate('');
        $buyer->setRegistrationAddress($address);
        $buyer->setIp('');
        $buyer->setCity($city);
        $buyer->setCountry($country);
        $buyer->setZipCode($zipcode);
        $req->setBuyer($buyer);

        $shippingAddress = new \Iyzipay\Model\Address();
        $shippingAddress->setContactName($name);
        $shippingAddress->setCity($city);
        $shippingAddress->setCountry($country);
        $shippingAddress->setAddress($address);
        $shippingAddress->setZipCode($zipcode);
        $req->setShippingAddress($shippingAddress);

        $billingAddress = new \Iyzipay\Model\Address();
        $billingAddress->setContactName($name);
        $billingAddress->setCity($city);
        $billingAddress->setCountry($country);
        $billingAddress->setAddress($address);
        $billingAddress->setZipCode($zipcode);
        $req->setBillingAddress($billingAddress);

        $q_id = uniqid(999, 99999);
        $basketItems = [];
        $firstBasketItem = new \Iyzipay\Model\BasketItem();
        $firstBasketItem->setId($q_id);
        $firstBasketItem->setName('Booking Id ' . $q_id);
        $firstBasketItem->setCategory1($title);
        $firstBasketItem->setCategory2('');
        $firstBasketItem->setItemType(\Iyzipay\Model\BasketItemType::PHYSICAL);
        $firstBasketItem->setPrice($calculatedData['grandTotal']);
        $basketItems[0] = $firstBasketItem;

        $req->setBasketItems($basketItems);

        # make request
        $payWithIyzicoInitialize = \Iyzipay\Model\PayWithIyzicoInitialize::create($req, $options);

        $paymentResponse = (array) $payWithIyzicoInitialize;
        foreach ($paymentResponse as $key => $data) {
            $paymentInfo = json_decode($data, true);
            if ($paymentInfo['status'] == 'success' && !empty($paymentInfo['payWithIyzicoPageUrl'])) {
                Cache::forget('conversation_id');
                Session::put('iyzico_token', $paymentInfo['token']);
                Session::put('conversation_id', $conversion_id);
                Cache::put('conversation_id', $conversion_id, 60000);

                // put some data in session before redirect to gateway
                Session::forget('paymentFor');
                Session::put('paymentFor', $paymentFor);
                Session::put('arrData', $arrData);

                return redirect($paymentInfo['payWithIyzicoPageUrl']);
            }
        }
        // put some data in session before redirect to gateway
        Session::forget('paymentFor');
        Session::put('paymentFor', $paymentFor);
        Session::put('arrData', $arrData);
    }

    public function notify(Request $request)
    {
        $conversation_id = Cache::get('conversation_id');
        $arrData = $request->session()->get('arrData');
        $arrData['conversation_id'] = $conversation_id;

        $paymentPurpose = $request->session()->get('paymentFor');
        if ($paymentPurpose == 'product purchase') {
            $productList = $request->session()->get('productCart');
        }

        if ($paymentPurpose == 'product purchase') {
            $purchaseProcess = new PurchaseProcessController();
            // store product order information in database
            $orderInfo = $purchaseProcess->storeData($productList, $arrData);
            return redirect()->route('shop.purchase_product.complete');
        } elseif ($paymentPurpose == 'equipment booking') {
            $bookingProcess = new BookingProcessController();

            // store equipment booking information in database
            $bookingInfo = $bookingProcess->storeData($arrData);

            return redirect()->route('equipment.make_booking.complete');
        }
    }
}
