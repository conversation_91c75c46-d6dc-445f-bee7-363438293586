
<link rel="stylesheet" href="<?php echo e(asset('assets/css/all.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/magnific-popup.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/slick.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/slick-theme.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/toastr.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/datatables-1.10.23.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/datatables.bootstrap4.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/jquery-ui.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/daterangepicker.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/default.min.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/floating-whatsapp.css')); ?>">

<link rel="stylesheet" href="<?php echo e(asset('assets/css/nice-select.css')); ?>">

<?php if($basicInfo->theme_version == 3 || $basicInfo->theme_version == 4 || $basicInfo->theme_version == 5 || $basicInfo->theme_version == 6): ?>
  <?php if(!request()->routeIs('index')): ?>
    <!-- Base CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/base.css')); ?>">
  <?php endif; ?>
<?php endif; ?>


<link rel="stylesheet" href="<?php echo e(asset('assets/css/main.css')); ?>">
<?php if(!request()->routeIs('index')): ?>
<!-- Innerpages CSS -->
<link rel="stylesheet" href="<?php echo e(asset('assets/css/inner-pages.css')); ?>">
<?php endif; ?>

<?php if($basicInfo->theme_version == 3 || $basicInfo->theme_version == 4 || $basicInfo->theme_version == 5 || $basicInfo->theme_version == 6): ?>
  <?php if(!request()->routeIs('index')): ?>
    <!-- Header CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/header/header.css')); ?>">
    <!-- Footer CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/footer/footer.css')); ?>">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/responsive.css')); ?>">
    <?php if($currentLanguageInfo->direction == 1): ?>
      <!-- RTL CSS -->
      <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/rtl.css')); ?>">
    <?php endif; ?>
  <?php endif; ?>
<?php endif; ?>


<link rel="stylesheet" href="<?php echo e(asset('assets/css/responsive.css')); ?>">

<?php if($currentLanguageInfo->direction == 1): ?>
  
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/rtl.css')); ?>">
  
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/rtl-responsive.css')); ?>">
<?php endif; ?>
<?php if($basicInfo->theme_version == 4): ?>
  <!-- Dark-theme CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/front/theme3456/css/theme-dark.css')); ?>">
<?php endif; ?>

<link rel="stylesheet" href="<?php echo e(asset("assets/css/website-color.php?primary_color=$primaryColor&secondary_color=$secondaryColor&breadcrumb_overlay_color=$breadcrumbOverlayColor")); ?>">
<?php /**PATH C:\xampp\htdocs\agapeconnect\resources\views/frontend/partials/styles.blade.php ENDPATH**/ ?>