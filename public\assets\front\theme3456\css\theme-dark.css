.theme-dark {
  --color-dark: #0A0909;
  --color-dark-rgb: 11, 10, 10;
  --color-medium: #3d3d3d;
  --text-dark: #f2f2f2;
  --text-medium: #ACACAC;
  --text-white: var(--color-white);
  --text-light: var(--color-light);
  --heading-color: var(--color-white);
  --border-color: #303030;
  --border-color-2: #3d3d3d;
  --bg-1: #0A0909;
  --bg-2: #161616;
  --bg-3: #212121;
  --bg-white: var(--color-dark);
}
.theme-dark * {
  box-shadow: none !important;
}
.theme-dark .bg-white {
  background-color: var(--color-dark) !important;
}
.theme-dark .lazy-container,
.theme-dark .bg-light {
  background-color: var(--bg-2) !important;
}
.theme-dark .bg-light-2 {
  background-color: var(--bg-3) !important;
}
.theme-dark .btn-outline::before,
.theme-dark .btn-primary::before {
  background-color: var(--color-white);
}
.theme-dark .btn-outline:hover,
.theme-dark .btn-primary:hover {
  color: var(--color-primary);
}
.theme-dark .card {
  --bs-card-bg: var(--bg-2);
}
.theme-dark .accordion {
  --bs-accordion-bg: var(--bg-2);
}
.theme-dark .nice-select .list {
  background-color: var(--bg-2);
}
.theme-dark .header-area.is-sticky {
  background: var(--bg-2);
  border-bottom: 1px solid var(--border-color);
}
.theme-dark .header-area .main-navbar .menu-dropdown {
  background-color: var(--bg-2);
  border: 1px solid var(--border-color);
}
.theme-dark .tabs-navigation .nav .nav-link {
  background-color: var(--bg-2);
}
.theme-dark .tabs-navigation .nav[data-hover=fancyHover] .nav-link:is(.active, :hover) {
  background-color: var(--bg-2);
}
.theme-dark .tabs-navigation-2 .nav {
  background-color: var(--bg-2);
}
.theme-dark .footer-area .copy-right-area.border-top {
  border-top-color: var(--border-color);
}
.theme-dark .testimonial-area .slider-item {
  background-color: var(--bg-2);
}
.theme-dark #preLoader {
  background-color: var(--bg-1);
}

body.theme-dark {
  color: var(--text-light);
}

input,
.table,
select option,
.form_control,
.nice-select:after,
.dropdown-menu .dropdown-item,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_paginate,
.checkout-area-section .form_group label,
.user-dashboard .user-sidebar .links li a,
.dataTables_wrapper .dataTables_processing,
.checkout-area-section .bottom .cart-table tbody input,
.user-area-section .user-form .form_group .form_control,
.cart-area-section .cart-table tbody .qty .quantity-input input,
.checkout-area-section .bottom .cart-total .cart-total-table li span.col,
.products-details-section .products-details-wrapper .quantity-input #product-quantity
{
  color: var(--text-light);
}

.card,
.preloader,
.modal-content,
.pricing-item-three,
.equipement-sidebar-info,
table.dataTable tbody tr,
.sidebar-widget-area .widget,
.checkout-area-section .coupon,
.blog-post-item-one .entry-content,
.faq-wrapper-one .card .card-header,
.cart-area-section .cart-table thead,
.checkout-area-section .bottom .cart-table tbody input,
.author-area .author-tabs .nav::-webkit-scrollbar-track,
.cart-area-section .cart-table tbody .qty .quantity-input input,
.user-dashboard .user-profile-details .edit-info-area .form_control,
.products-details-section .products-details-wrapper .quantity-input #product-quantity{
  background-color: var(--bg-2);
}

.form_control,
.dropdown-menu,
.author-area .authors-search-filter .search-filter-form .nice-select,
.pricing-item-three .pricing-info .pricing-body .price-option span.span-btn,
.sidebar-widget-area .widget.price-range-widget .ui-widget.ui-widget-content,
.equipement-sidebar-info .booking-form .pricing-body .price-option span.span-btn,
.equipment-details-section .pricing-body .form_group input:not(#offline-gateway-attachment)
{
  background-color: var(--bg-3);
}

.nav-tabs,
.modal-header,
.table thead th,
.information-item,
.pricing-item-three,
.user-dashboard .title,
.user-dashboard .user-sidebar,
.user-dashboard .account-info,
.user-dashboard .order-details,
.daterangepicker .calendar-table,
.faq-wrapper-one .card .card-body,
.checkout-area-section .payment-options,
.cart-area-section .cart-table tbody tr td,
.contact-wrapper .contact-form .form_control,
.dataTables_wrapper .dataTables_filter input,
.dataTables_wrapper .dataTables_length select,
.checkout-area-section .form_group .form_control,
.checkout-area-section .bottom .cart-table tbody input,
.user-area-section .user-form .form_group .form_control,
.checkout-area-section .bottom .cart-total .cart-total-table,
.cart-area-section .cart-table tbody .qty .quantity-input input,
.checkout-area-section .bottom .cart-total .cart-total-table li,
.user-dashboard .user-profile-details .edit-info-area .form_control,
.description-wrapper .features-content-box .content-table .table td,
.cart-area-section .cart-table tbody .qty .quantity-input .quantity-up,
.cart-area-section .cart-table tbody .qty .quantity-input .quantity-down,
.checkout-area-section .bottom .cart-total .cart-total-table li span.col.col-title,
.equipment-details-section .pricing-body .form_group input:not(#offline-gateway-attachment),
.user-dashboard .main-table .dataTables_wrapper .dataTables_paginate .paginate_button .page-link
{
  border-color: var(--border-color);
}

.pagination .page-link{
  border: unset;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link
{
  background-color: transparent;
}

.author-area .authors-search-filter .search-filter-form .nice-select{
  border: unset;
}
.cart-wrapper{
  display: none;
}
