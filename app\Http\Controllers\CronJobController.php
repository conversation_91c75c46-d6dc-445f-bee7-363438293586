<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Shop\ProductOrder;
use App\Http\Controllers\Controller;
use App\Jobs\IyzicoEquipmentPendingPayment;
use App\Models\Instrument\EquipmentBooking;
use App\Jobs\IyzicoProductOrderPendingPayment;

class CronJobController extends Controller
{
    public function checkIyzicoPendingPayment()
    {
        try {
            /*```````````````````````````````````````````````````````
            ```````````Check Iyzico pending bookings``````````
            -------------------------------------------------------*/
            $booking = EquipmentBooking::where([['payment_status', 'pending'], ['payment_method', 'Iyzico']])->get();
            
            if (count($booking) > 0) {
                foreach ($booking as $key => $equipment_booking) {
                    if (!is_null($equipment_booking->conversation_id)) {
                        IyzicoEquipmentPendingPayment::dispatch($equipment_booking->id);
                    }
                }
            }
            /*```````````````````````````````````````````````````````
            ```````````Check Iyzico product purchase pending bookings``````````
            -------------------------------------------------------*/
            $productOrders = ProductOrder::where([['payment_status', 'pending'], ['payment_method', 'Iyzico']])->get();
            if (count($productOrders) > 0) {
                foreach ($productOrders as $key => $productOrder) {
                    if (!is_null($productOrder->conversation_id)) {
                        IyzicoProductOrderPendingPayment::dispatch($productOrder->id);
                    }
                }
            }
        } catch (\Throwable $th) {
            dd($th);
        }
    }
}
