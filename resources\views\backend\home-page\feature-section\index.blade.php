@extends('backend.layout')

{{-- this style will be applied when the direction of language is right-to-left --}}
@includeIf('backend.partials.rtl-style')

@section('content')
    <div class="page-header">
        <h4 class="page-title">{{ __('Feature Section') }}</h4>
        <ul class="breadcrumbs">
            <li class="nav-home">
                <a href="{{ route('admin.dashboard') }}">
                    <i class="flaticon-home"></i>
                </a>
            </li>
            <li class="separator">
                <i class="flaticon-right-arrow"></i>
            </li>
            <li class="nav-item">
                <a href="#">{{ __('Home Page') }}</a>
            </li>
            <li class="separator">
                <i class="flaticon-right-arrow"></i>
            </li>
            <li class="nav-item">
                <a href="#">{{ __('Feature Section') }}</a>
            </li>
        </ul>
    </div>

    <div class="row">
        @if ($settings->theme_version == 1||$settings->theme_version == 2)
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-lg-10">
                                <div class="card-title">{{ __('Update Feature Section') }}</div>
                            </div>

                            <div class="col-lg-2">
                                @includeIf('backend.partials.languages')
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="featureForm"
                                    action="{{ route('admin.home_page.update_feature_section', ['language' => request()->input('language')]) }}"
                                    method="POST">
                                    @csrf
                                    <div class="row">
                                        <div class="col">
                                            <div class="form-group">
                                                <label for="">{{ __('Subtitle') }}</label>
                                                <input type="text" class="form-control" name="subtitle"
                                                    value="{{ empty($data->subtitle) ? '' : $data->subtitle }}"
                                                    placeholder="Enter Subtitle">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col">
                                            <div class="form-group">
                                                <label for="">{{ __('Title') }}</label>
                                                <input type="text" class="form-control" name="title"
                                                    value="{{ empty($data->title) ? '' : $data->title }}"
                                                    placeholder="Enter Title">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col">
                                            <div class="form-group">
                                                <label for="">{{ __('Text') }}</label>
                                                <textarea class="form-control" name="text" rows="5" placeholder="Enter Text">{{ empty($data->text) ? '' : $data->text }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" form="featureForm" class="btn btn-success">
                                    {{ __('Update') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        @if ($settings->theme_version == 1||$settings->theme_version == 2 ||$settings->theme_version == 3 ||$settings->theme_version == 5)
        <div class="col-md-{{ $settings->theme_version == 3 || $settings->theme_version == 5 ? '12' : '6' }}">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card-title">{{ __('Features') }}</div>
                        </div>

                        <div class="col-lg-3">
                            @includeIf('backend.partials.languages')
                        </div>

                        <div class="col-lg-4 offset-lg-1 mt-2 mt-lg-0">
                            <a href="#" data-toggle="modal" data-target="#createModal"
                                class="btn btn-primary btn-sm float-lg-right float-left"><i class="fas fa-plus"></i>
                                {{ __('Add') }}</a>

                            <button class="btn btn-danger btn-sm float-right mr-2 d-none bulk-delete"
                                data-href="{{ route('admin.home_page.bulk_delete_feature') }}">
                                <i class="flaticon-interface-5"></i> {{ __('Delete') }}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            @if (count($features) == 0)
                                <h3 class="text-center mt-2">{{ __('NO FEATURE FOUND') . '!' }}</h3>
                            @else
                                <div class="table-responsive">
                                    <table class="table table-striped mt-3" id="basic-datatables">
                                        <thead>
                                            <tr>
                                                <th scope="col">
                                                    <input type="checkbox" class="bulk-check" data-val="all">
                                                </th>
                                                <th scope="col">{{ __('Icon') }}</th>
                                                <th scope="col">{{ __('Title') }}</th>
                                                @if ($settings->theme_version == 1||$settings->theme_version == 2||$settings->theme_version == 3)
                                                <th scope="col">{{ __('Text') }}</th>
                                                @endif
                                                <th scope="col">{{ __('Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($features as $feature)
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" class="bulk-check"
                                                            data-val="{{ $feature->id }}">
                                                    </td>
                                                    <td><i class="{{ $feature->icon }}"></i></td>
                                                    <td>
                                                        {{ strlen($feature->title) > 30 ? mb_substr($feature->title, 0, 30, 'UTF-8') . '...' : $feature->title }}
                                                    </td>
                                                    @if ($settings->theme_version == 1||$settings->theme_version == 2 ||$settings->theme_version == 3)
                                                    <td>
                                                        {{ strlen($feature->text) > 50 ? mb_substr($feature->text, 0, 50, 'UTF-8') . '...' : $feature->text }}
                                                    </td>
                                                    @endif
                                                    <td>
                                                        <a class="btn btn-secondary btn-sm mr-1 editBtn" href="#"
                                                            data-toggle="modal" data-target="#editModal"
                                                            data-id="{{ $feature->id }}"
                                                            data-icon="{{ $feature->icon }}"
                                                            data-title="{{ $feature->title }}"
                                                            data-text="{{ $feature->text }}">
                                                            <span class="btn-label">
                                                                <i class="fas fa-edit"></i>
                                                            </span>
                                                        </a>

                                                        <form class="deleteForm d-inline-block"
                                                            action="{{ route('admin.home_page.delete_feature', ['id' => $feature->id]) }}"
                                                            method="post">
                                                            @csrf
                                                            <button type="submit"
                                                                class="btn btn-danger btn-sm deleteBtn">
                                                                <span class="btn-label">
                                                                    <i class="fas fa-trash"></i>
                                                                </span>
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-footer"></div>
            </div>
        </div>
        @endif
    </div>

    {{-- create modal --}}
    @include('backend.home-page.feature-section.create')

    {{-- edit modal --}}
    @include('backend.home-page.feature-section.edit')
@endsection
