/* -----------------------------------------
	Hero Banner 2
----------------------------------------- */
.hero-banner.hero-banner_v5 {
  padding-top: 163px;
  padding-bottom: 100px;
}
.hero-banner.hero-banner_v5 .banner-content .subtitle {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}
.hero-banner.hero-banner_v5 .banner-content .subtitle .line {
  position: relative;
  overflow: hidden;
  display: inline-block;
  border-inline-start: 6px solid var(--color-primary);
  width: 26px;
  height: 18px;
  margin-inline-start: 4px;
  transform: skewX(-10deg);
}
.hero-banner.hero-banner_v5 .banner-content .subtitle .line::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  bottom: 0;
  width: 6px;
  height: 100%;
  margin-inline-start: 4px;
  background-color: var(--color-primary);
}
.hero-banner.hero-banner_v5 .banner-content .subtitle .line::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 6px;
  height: 100%;
  margin-inline-start: auto;
  margin-inline-end: 0;
  background-color: var(--color-primary);
}
.hero-banner.hero-banner_v5 .banner-content .product-price {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.hero-banner.hero-banner_v5 .banner-content .product-price h6 {
  width: 100%;
}
.hero-banner.hero-banner_v5 .banner-content .product-price * {
  margin: 0;
  line-height: 1;
}
.hero-banner.hero-banner_v5 .banner-content .product-price .prev-price {
  text-decoration: line-through;
  font-weight: var(--font-semi-bold);
}
.hero-banner.hero-banner_v5 .form-wrapper {
  background-color: var(--bg-white);
  z-index: 3;
}
.hero-banner.hero-banner_v5 .right-content {
  position: relative;
  overflow: hidden;
}
.hero-banner.hero-banner_v5 .right-content .overlap {
  position: absolute;
  overflow: hidden;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-inline-start: auto;
  width: 57.1428571429%;
  height: 100%;
  background-color: var(--color-primary);
  z-index: -1;
}
.hero-banner.hero-banner_v5 .right-content .overlap::before, .hero-banner.hero-banner_v5 .right-content .overlap::after {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: var(--radius-circle);
  border: 10px solid var(--color-white);
}
.hero-banner.hero-banner_v5 .right-content .overlap::after {
  top: 0;
  margin-inline-start: auto;
  margin-inline-end: 20px;
  margin-top: -15%;
}
.hero-banner.hero-banner_v5 .right-content .overlap::before {
  bottom: 0;
  margin-inline-end: auto;
  margin-inline-start: 20px;
  margin-bottom: -15%;
}
.hero-banner.hero-banner_v5 .right-content .swiper-slide {
  position: relative;
  overflow: hidden;
}
.hero-banner.hero-banner_v5 .right-content .home-img-slider {
  position: relative;
}
.hero-banner.hero-banner_v5 .pagination-fraction .swiper-pagination-bullet {
  color: var(--text-white);
}
.hero-banner.hero-banner_v5 .shape > * {
  position: absolute;
  z-index: -1;
}
.hero-banner.hero-banner_v5 .shape > *.shape-1 {
  top: 14%;
  left: 35%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-2 {
  top: 45%;
  left: 45%;
  animation: moveUpDown 5s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-3 {
  bottom: 10%;
  left: 10%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-4 {
  bottom: 5%;
  left: 36%;
  animation: moveLeftRight 8s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-5 {
  top: 40%;
  right: 10%;
  animation: moveUpDown 10s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-6 {
  top: 40%;
  right: 25%;
  animation: moveLeftRight 10s linear infinite;
}
.hero-banner.hero-banner_v5 .shape > *.shape-7 {
  top: 65%;
  right: 5%;
  animation: moveUpDown 10s linear infinite;
}